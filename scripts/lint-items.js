/* eslint-disable max-len, max-lines */
import {
    matchInvalidModuleImportOrder,
    matchInvalidComponentImportOrder,
    matchInvalidModuleDirImportFromSelf,
    matchFilePathTooLong,
    matchLess,
    matchPandaDesign,
} from './lint-utils.js';

export const lintItems = [
    {
        // eslint-disable-next-line max-len
        label: 'comatestack/ievalue/iplayground 相互不引用，页面和页面不相互引用，需要复用的部分提取成 components',
        lintFunction: matchInvalidModuleImportOrder,
        ignores: [
            // TopNav 引用 router 是合理的
            'src/comatestack/AppLayout/TopNav/TopNav.tsx',
            'src/ievalue/App/Header/TopNav.tsx',
            'src/iplayground/TopNavigation/Header.tsx',
            'src/label-view/providers/useAnnotationActions.tsx',
            'src/label-view/providers/useBatchAnnotationActions.tsx',
            // 实验室后续可以用 useMatch
            'src/ievalue/LabPages/LabPromptDiagnosis.tsx',
            'src/ievalue/LabPages/LabPromptOptimize.tsx',
            'src/ievalue/LabPages/LabPromptNormalOptimize.tsx',
            // ievalue 存量代码中相互引用的情况比较多，在迁移过程中不要求全部修复，在尽量修复的过程中，如果有不好搞的，暂时忽略，后续再逐步修复
            'src/iplayground/PipelineSnapshotPage/PipelineSnapshotPage.tsx',
            'src/ievalue/PromptOptimize/OptimizeResult/ResultContent/ResultInfo.tsx',
            'src/ievalue/PromptOptimize/AutomaticOptimizeForm/SamplesFormItem/ExtractButton/ExtractModal.tsx',
            'src/ievalue/PromptOptimize/AutomaticOptimizeForm/HighConfig/EvaluateConfig/EvalStandardField.tsx',
            'src/ievalue/PromptOptimize/AutomaticOptimizeForm/SamplesFormItem/ImportSample/ImportSampleModal.tsx',
            'src/ievalue/PromptOptimize/AutomaticOptimizeForm/SamplesFormItem/ImportSample/PromptImageSelect.tsx',
            'src/ievalue/PromptOptimize/AutomaticOptimizeForm/HighConfig/EvaluateConfig/EvalStandardTextAreePanel.tsx',
            '/src/ievalue/PromptOptimize/AutomaticOptimizeForm/HighConfig/EvaluateConfig/EvalStandardGeneralButton.tsx',
            '/src/comatestack/ProjectHome/Home/index.tsx',
            '/src/comatestack/StaffInline/ChatPanel/index.tsx',
            '/src/ievalue/TaskDetail/CaseProcessPanel/ModelResult/DiffTag.tsx',
            '/src/ievalue/TaskDetail/ChatbotCaseProcessPanel/Chat/MsgList/MsgItemScoreItem/DiffTag.tsx',
            '/src/ievalue/TaskDetail/ChatbotCaseProcessPanel/Chat/Score/DiffTag.tsx',
            '/src/ievalue/TaskDetail/ChatbotCaseProcessPanel/Chat/Score/ScoreGroup.tsx',
            '/src/ievalue/TaskDetail/ChatbotCaseProcessPanel/Chat/Score/SessionAutoScoreGroup.tsx',
            '/src/ievalue/TaskDetail/ChatbotCaseProcessPanel/ChatbotCaseProcessOffline/ChatPanel/MsgItemScoreItem/DiffTag.tsx',
            '/src/ievalue/TaskDetail/TaskGroupDetail/components/AutoComputePanelComponent/AutoComputePanel.tsx',
            '/src/ievalue/TaskDetail/TaskGroupDetail/components/ScoreComponent/ScoreCheckbox/index.tsx',
            '/src/ievalue/TaskDetail/TaskGroupDetail/components/ScoreComponent/ScoreSelect/index.tsx',
            '/src/ievalue/TaskDetail/TaskGroupDetail/GroupFeatureCaseDetail/ModelResult/FeatureScoreSelect.tsx',
            '/src/ievalue/TaskDetail/TaskGroupDetail/components/ScoreComponent/ScoreSelectNew/index.tsx',
            '/src/ievalue/TaskDetail/TaskGroupDetail/components/ScoreComponent/ScoreInput/index.tsx',
            '/src/ievalue/TaskDetail/TaskGroupDetail/components/ScoreComponent/CaseDispatchAuditingSelect/index.tsx',
            '/src/ievalue/TaskDetail/TaskGroupDetail/components/ScoreComponent/CaseDispatchAuditingSelect/DiffPanel.tsx',
            '/src/ievalue/TaskDetail/TaskDetailPage/components/TaskStageAssignButton/TaskStageAssignContent.tsx',
        ],
    },
    {
        // eslint-disable-next-line max-len
        label: 'components 等可复用代码，不应该引用到页面级别的代码(comatestack/ievalue/iplayground)',
        lintFunction: matchInvalidComponentImportOrder,
        ignores: [
            'src/components/ievalue/PromptModelSelect/ModelMutableSelect/ModelSearchList.tsx',
            'src/hooks/ievalue/prompt.ts',
            'src/components/ievalue/ModelsSelect/SelectDispaly.tsx',
            'src/components/ievalue/RelationCard/index.tsx',
            'src/components/Prompt/NewModelServiceButton.tsx',
            'src/components/ievalue/ICafeCardFormItem/index.tsx',
            'src/components/Prompt/CreatePrompt/SpaceCodeField.tsx',
            'src/components/Prompt/Debugging/components/ModelPanel.tsx',
            'src/components/Prompt/Debugging/components/PromptVersionPanel.tsx',
        ],
    },
    {
        label: '代码文件路径最大深度小于 7 层',
        lintFunction: matchFilePathTooLong,
        ignores: [
            'src/components/Evaluate/TaskTagEditor/TagEditorTrigger/TagSearch/index.tsx',
            'src/components/Evaluate/TaskTagEditor/TagEditorTrigger/TagSearch/TagItem.tsx',
            // 目前暂停开发，再开发的时候把层级重新搞一下
            'src/comatestack/ProjectHome/components/EntryList/EntryCard/DetailButton.tsx',
            'src/comatestack/ProjectHome/components/EntryList/EntryCard/ExperienceButton.tsx',
            'src/comatestack/ProjectHome/components/EntryList/EntryCard/FavoriteStar.tsx',
            'src/comatestack/ProjectHome/components/EntryList/EntryCard/FirstWordIcon.tsx',
            'src/comatestack/ProjectHome/components/EntryList/EntryCard/index.tsx',
            'src/comatestack/ProjectHome/components/EntryList/EntryCard/TagslPanel.tsx',
            'src/comatestack/ProjectHome/Home/FastEntry/CollectionEntry/index.tsx',
            'src/comatestack/ProjectHome/Home/FastEntry/DataEntry/index.tsx',
            'src/comatestack/ProjectHome/Home/FastEntry/EvaluateCard/index.tsx',
            'src/comatestack/ProjectHome/Home/FastEntry/EvaluateEntry/EvaluateIntroEntry/index.tsx',
            'src/comatestack/ProjectHome/Home/FastEntry/EvaluateEntry/Filter/index.tsx',
            'src/comatestack/ProjectHome/Home/FastEntry/EvaluateEntry/Filter/SelectPanel.tsx',
            'src/comatestack/ProjectHome/Home/FastEntry/EvaluateEntry/FilterButton/index.tsx',
            'src/comatestack/ProjectHome/Home/FastEntry/EvaluateEntry/index.tsx',
            'src/comatestack/ProjectHome/Home/WaitingTask/TaskList/DetailButton.tsx',
            'src/comatestack/ProjectHome/Home/WaitingTask/TaskList/FastStartButton.tsx',
            'src/comatestack/ProjectHome/Home/WaitingTask/TaskList/index.tsx',
            'src/comatestack/ProjectHome/Home/WaitingTask/TaskList/useColumns.tsx',
            'src/comatestack/ProjectHome/Home/WaitingTask/TaskList/useTaskList.tsx',
        ],
    },
    {
        label: '同一模块下使用相对路径引用，如 \'@/components/A/B\' 可改为 \'./B\'',
        lintFunction: matchInvalidModuleDirImportFromSelf,
    },
    {
        label: 'api 接口类型未添加',
        lintFunction: content => /Interface\(/.exec(content),
        ignores: ['src/utils/icode/api/__tests__/createInterface.test.ts'],
    },
    {
        label: '不从主入口导入 @/baidu/devops-design，它会导致一些样式的 bug',
        lintFunction: content => /'@baidu\/devops-design'/.exec(content),
    },
    {
        label: '尽可能使用 @panda-design/components，包括 Button, message, Modal',
        lintFunction: matchPandaDesign,
        ignores: [
            'src/styles/appendIframeStyle.ts',
            'src/utils/createInterface/sessionLost.tsx',
            'src/hooks/icode/webIDE/useRestartResource.tsx',
            'src/components/icode/SubmitSetting/ImpactButton.tsx',
            'src/icode/AccountSettings/CodeScanExempt/ApplicationForm/ExemptPathAdder.tsx',
            'src/icode/DatasetFileList/StatusBar/BatchSearchPreviewDrawer/PublishModal.tsx',
            'src/icode/DatasetFileList/UploadModal/FileListTable/FileTagPopoverContent.tsx',
            'src/icode/IDEAIDevelopment/AIResourceCard/Header.tsx',
            'src/icode/IDEMachine/CloudDriveOperations/DeleteModal/index.tsx',
            'src/icode/IDEMachine/CloudDriveOperations/MountModal/index.tsx',
            'src/icode/IDEMachine/CloudDriveOperations/UninstallModal/index.tsx',
            'src/icode/IDEMachine/VMDeleteModal/index.tsx',
            'src/icode/RepoScan/RepoApi/DetailInformation/RepairAPI.tsx',
            'src/icode/RepoScan/RepoRiskyDependencies/ExemptModal.tsx',
            'src/icode/RepoScan/RepoRiskyDependencies/RiskDependencyTable.tsx',
            'src/icode/WebIDE/DriveApply/Actions.tsx',
        ],
    },
    // 以下规则一般都不太触发，仅为了防止重现
    {
        label: '不能使用 UNSAFE_ 生命周期',
        lintFunction: content => /UNSAFE_/.exec(content),
    },
    {
        label: 'substr 已废弃，用 substring 代替',
        lintFunction: content => /substr\(/.exec(content),
    },
    {
        label: '不使用 less，用 @emotion 代替',
        lintFunction: matchLess,
        ignores: [
            'src/types/global.d.ts',
            'src/components/ievalue/LoadingText/index.tsx',
            'src/components/ievalue/MDFullScreen/index.tsx',
            'src/components/ievalue/ScratchWords/index.tsx',
            'src/components/ievalue/ScratchWords/PopCreate/index.tsx',
            'src/components/ievalue/ScratchWords/PopTextArea/index.tsx',
            'src/components/ievalue/UserList/index.tsx',
            'src/components/Prompt/Debugging/components/FrameworkButton/index.tsx',
            'src/styles/ievalue/index.ts',
        ],
    },
    {
        // lodash/fp 的类型推断太差，且有初始化的时间
        label: '不使用 lodash/fp，用 lodash 代替',
        lintFunction: content => /'lodash\/fp'/.exec(content),
    },
    {
        label: '不使用 localStorage，用 createRegion(..., {withLocalStorageKey: \'key\'}) 代替',
        lintFunction: content => /window\.localStorage/.exec(content),
    },
    {
        label: '统一使用 Function Component，不使用 Class Component （特殊情况添加至白名单）',
        lintFunction: content => /class .*Component/.exec(content),
        ignores: [
            'src/design/CodeEditor/Render.tsx',
            'src/design/icode/ErrorPages/Boundary.tsx',
        ],
    },
    {
        label: '不使用 propTypes',
        lintFunction: content => /propTypes/.exec(content),
    },
    {
        label: '不使用 defaultProps',
        lintFunction: content => /defaultProps/.exec(content),
    },
    {
        label: '不使用 ?react，改为 yarn generate-icons',
        lintFunction: content => content.includes('?react'),
    },
    // 不知道为啥 eslint 无效，单独写一个规则
    {
        label: '不使用 == 和 !=，改为 === 和 !==',
        lintFunction: content =>
            content.includes(' == ') || content.includes(' != '),
        ignores: [
            'src/constants/ievalue/prompt.ts',
            'src/icode/WebIDE/ManagePortModal/AddPort.tsx',
            'src/third-party/ernie/formatWordMarkConfig.ts',
            'src/icode/IDEMachine/MachineList/MachineCard/index.tsx',
            'src/icode/IDEMachine/MachineList/MachineDeleteModal/index.tsx',
            'src/icode/IDEMachine/MachineList/MachineOperations/index.tsx',
            'src/icode/IDEMachine/MachineList/useRestartWorkspaceHandler.ts',
            'src/icode/IDEMachine/MachineList/WorkspaceDeleteModal/index.tsx',
            'src/icode/Approval/Columns/GetAuditPhase.tsx',
            'src/constants/icode/highlightTheme.ts',
        ],
    },
    {
        label: '不从 params 获取 spaceCode，改为 useSpaceCodeSafe 或 useSpaceCode',
        lintFunction: content => /spaceCode.*useParams/.exec(content),
        ignores: ['src/hooks/ievalue/spacePage.ts'],
    },
    {
        label: '不 import 整个 React，改为 import 实际用到的部分',
        lintFunction: content => /import React /.exec(content) || /import React,/.exec(content) || /React\./.exec(content),
        ignores: ['src/icons/comatestack/index.ts'],
    },
];
