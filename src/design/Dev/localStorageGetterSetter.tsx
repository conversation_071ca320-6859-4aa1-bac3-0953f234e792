/* eslint-disable no-console */
export const envMap = {
    sandbox: '沙盒环境',
    'icode-sandbox': '沙盒环境-icode',
    'mcp-sandbox': '沙盒环境-MCP',
    'staff-sandbox': '沙盒环境-数字员工',
    ernieDev: '一言dev',
    canary: '金丝雀环境',
    weizhen02: '魏臻',
    huyufei: '宇飞',
    marongzhen: '荣臻',
    shenlin09: '申琳',
    liuzhao03: '刘召',
    yangyichen01: '翌晨',
    xuyuan09: '许愿',
    zhangenming02: '恩铭',
    zhangcong06: '张聪',
    wangkaiyuan02: '开源',
    kangyuxin: '育鑫',
    liqiyi: '齐一',
    yandong02: '闫冬',
    dongtaomin: '陶民',
    dingzhe: '丁哲',
    liancong: '连聪',
    wangkun21: '王坤',
};

export const getEnvValueFromLocalStorage = (): string | undefined => {
    try {
        const productConfigsValue = localStorage.getItem('@ls-storage-should-clear-lab-products');
        const productConfigs = JSON.parse(productConfigsValue);
        const {hostname} = new URL(productConfigs?.comatestack?.entry);
        for (const env of Object.keys(envMap)) {

            if (hostname.startsWith(`comatestack-${env}`)) {
                return env;
            }
        }
    }
    catch {
        // do nothing
    }
};

export const setLocalStorageEnvValue = (value: string): void => {
    if (value === 'sandbox') {
        localStorage.removeItem('@ls-storage-should-clear-lab-products');
        window.location.reload();
        return;
    }
    const productConfigs = {
        comatestack: {
            entry: `https://comatestack-${value}.now.baidu-int.com/index-comatestack.html`,
        },
        iplayground: {
            entry: `https://comatestack-${value}.now.baidu-int.com/index-iplayground.html`,
        },
        ievalue: {
            entry: `https://comatestack-${value}.now.baidu-int.com/index-ievalue.html`,
        },
        icode: {
            entry: `https://comatestack-${value}.now.baidu-int.com/index-icode.html`,
        },
    };
    const productConfigsValue = JSON.stringify(productConfigs);
    localStorage.setItem('@ls-storage-should-clear-lab-products', productConfigsValue);
};
