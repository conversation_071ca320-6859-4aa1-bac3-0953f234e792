/**
 * @file 合并分支页面中切换的tabs
 */
import {useCallback, useEffect, useMemo, useState} from 'react';
import {Col, Divider, Row, Select, Tabs, Tooltip, Typography} from 'antd';
import {marginTop} from '@panda-design/components';
import {css} from '@emotion/css';
import FileChange from '@/components/icode/FileChange';
import {Loading} from '@/design/icode/Loading';
import {useCurrentRepoName} from '@/regions/icode/currentRepo';
import {useSearchParams} from '@/hooks/icode/common/useSearchParams';
import useCurrentMergeRange from '@/hooks/icode/current/useCurrentMergeRange';
import {useNavigateReplace} from '@/hooks/icode/common/useNavigate';
import {useCurrentPath} from '@/hooks/icode/current/useCurrentPath';
import {useDigitShortKeys} from '@/hooks/icode/shortKeys/useDigitShortKeys';
import {
    useMergeCompareInfo,
    useMergeCompareInfoLoading,
    useMergeBase,
    loadMergeBase,
} from '@/regions/icode/merge/mergeCompareInfo';
import {getPrefixByRepoName} from '@/utils/icode/route/getPrefixByRepoName';
import {Help} from '@/design/Help';
import {IconHelp} from '@/icons/lucide';
import useIsMergeRequestPage from '../useIsMergeRequestPage';
import {CompareModeType, useCompareModeContext} from '../MergeOperations/provider';
import CommitList from './MergeDetailCommitListContainer';

const {Text} = Typography;

const compareModeCss = css`
    margin-top: 30px;
`;

const MergeDetail = () => {
    const navigateReplace = useNavigateReplace();
    const repoName = useCurrentRepoName();
    const {from, to} = useCurrentMergeRange();
    const isMergeRequestPage = useIsMergeRequestPage();
    const loading = useMergeCompareInfoLoading();
    const mergeInfo = useMergeCompareInfo();
    const mergeBaseCommitId = useMergeBase();
    // 后续迁移到 nextPath
    const {path: prePath} = useSearchParams();
    const nextPath = useCurrentPath();
    const path = prePath ?? nextPath;
    const [activeKey, setActiveKey] = useState(path ? 'code' : 'version');
    const {compareModeState, changeCompareModeToDirect, changeCompareModeToMergeBase} = useCompareModeContext();
    const setCompareMode = useCallback(
        (value: CompareModeType) => {
            if (value === 'direct') {
                changeCompareModeToDirect();
            } else {
                changeCompareModeToMergeBase();
            }
        },
        [changeCompareModeToDirect, changeCompareModeToMergeBase]
    );

    const handleTabVersion = useCallback(
        () => {
            setActiveKey('version');
        },
        []
    );

    const handleTabCode = useCallback(
        () => {
            setActiveKey('code');
        },
        []
    );

    useDigitShortKeys([handleTabVersion, handleTabCode]);

    // 在 merge-base 模式下加载 merge-base commit
    useEffect(
        () => {
            if (compareModeState === 'merge-base' && repoName && mergeInfo?.fromCommitId && mergeInfo?.toCommitId) {
                loadMergeBase({repo: repoName, fromRef: mergeInfo.fromCommitId, toRef: mergeInfo.toCommitId});
            }
        },
        [compareModeState, repoName, mergeInfo?.fromCommitId, mergeInfo?.toCommitId]
    );

    const handlePathChange = useCallback(
        (path: string) => {
            const encodedTo = encodeURIComponent(to);
            const encodedFrom = encodeURIComponent(from);
            const query = path ? `/${path}` : '';
            const type = isMergeRequestPage ? 'mergeRequest' : 'merge';
            const prefix = getPrefixByRepoName(repoName);
            navigateReplace(`/${prefix}/${type}/${encodedTo}...${encodedFrom}${query}`);
        },
        [from, isMergeRequestPage, navigateReplace, repoName, to]
    );

    const versionTabPaneContent = useMemo(
        () => {
            const tip = compareModeState === 'direct'
                ? (
                    <>
                        显示两个分支的所有提交记录差异，包括双方各自的历史提交。<br />
                        🔍 适合深入分析两个分支的完整演进历史，了解所有开发轨迹。<br />
                        💡 对应命令：<code>[ git log 目的分支...源分支 ]</code>
                    </>
                )
                : (
                    <>
                        只显示源分支独立演进的提交记录，查看&quot;这个分支都做了什么新功能&quot;。<br />
                        📋 适合代码评审和进度跟踪，清晰聚焦本次开发内容。<br />
                        💡 对应命令：<code>[ git log $(git merge-base 源分支 目的分支)..源分支 ]</code>
                    </>
                );
            return <span>版本差异<Help>{tip}</Help></span>;
        },
        [compareModeState]
    );

    const codeTabPaneContent = useMemo(
        () => {
            const tip = compareModeState === 'direct'
                ? (
                    <>
                        显示两个分支代码的完整差异，包括双方所有不同的文件和代码行。<br />
                        📊 适合全面对比两个版本，分析复杂的分支分歧情况。<br />
                        💡 对应命令：<code>[ git diff 目的分支..源分支 ]</code>
                    </>
                )
                : (
                    <>
                        只显示源分支修改的代码内容，与日常代码评审中看到的代码变更形式类似。<br />
                        📝 适合代码评审、功能验证和变更确认，是日常开发的首选方式。<br />
                        💡 对应命令：<code>[ git diff $(git merge-base 源分支 目的分支)..源分支 ]</code>
                    </>
                );
            return <span>代码差异<Help>{tip}</Help></span>;
        },
        [compareModeState]
    );

    if (loading) {
        return <Loading />;
    }

    const {toCommitId, fromCommitId} = mergeInfo ?? {};
    // 切换分支可能导致竞态
    // Tabs 影响 内容的 sticky
    return (
        <>
            <Row align="middle">
                <Col className={compareModeCss}>
                    {/* 这里写死宽度是为了和MergeBranches组件中的表单对齐, Select的宽度PM特殊要求了。仅对齐label */}
                    <Row align="middle">
                        <Col flex="80px">
                            <Text>对比方式</Text>
                            <Tooltip
                                overlayStyle={{maxWidth: 500}}
                                title={(
                                    <>
                                        <p>
                                            <b>【增量改动】：</b>
                                            只显示源分支相比目标分支从共同祖先（基准点）分叉后，
                                            独立演进的变更内容，就像代码评审时看到的差异一样。
                                            了解哪些内容会被合入到目标分支中。
                                        </p>
                                        <p style={{color: '#666'}}>
                                            📝 <b>适用场景：</b>代码评审、合并请求审查、查看功能开发进度
                                        </p>
                                        <p style={{color: '#666'}}>
                                            ✨ <b>优势：</b>专注于本分支开发的改动，避免无关历史干扰，是最常用的对比方式
                                        </p>
                                        <br />
                                        <p>
                                            <b>【完整差异】：</b>
                                            显示两个分支之间的所有不同，包括双方各自独有的全部改动。
                                        </p>
                                        <p style={{color: '#666'}}>
                                            📝 <b>适用场景：</b>分析两个长期分离的分支、解决复杂冲突、全面了解代码区别
                                        </p>
                                        <p style={{color: '#666'}}>
                                            💡 <b>注意：</b>可能包含大量历史改动，信息较多，建议有经验的开发者使用
                                        </p>
                                        <br />
                                        <p>
                                            ⚠️
                                            Git合并机制复杂，此页面无法即时展示最终结果，
                                            请操作合并后检查目标分支的最终代码，或利用iPipe合入后流水线
                                            （如MasterPipeline、BranchPipeline等）验证编译和测试，避免遗漏重要信息
                                        </p>
                                    </>
                                )}
                            >
                                <IconHelp style={{marginLeft: 4, cursor: 'pointer'}} />
                            </Tooltip>
                        </Col>
                        <Col flex="150px">
                            <Tooltip
                                title={compareModeState === 'merge-base'
                                    ? '只显示当前源分支独立变更的改动，适合代码评审和日常开发'
                                    : '显示两个分支的完整差异，适合深入分析分支历史'
                                }
                                placement="top"
                                mouseEnterDelay={0}
                                mouseLeaveDelay={0.1}
                            >
                                <Select value={compareModeState} onChange={setCompareMode} style={{width: '160px'}}>
                                    <Select.Option key="merge-base" value="merge-base">
                                        增量改动(默认·推荐)
                                    </Select.Option>
                                    <Select.Option key="direct" value="direct">
                                        完整差异
                                    </Select.Option>
                                </Select>
                            </Tooltip>
                        </Col>
                    </Row>
                    <Divider type="vertical" style={{position: 'absolute', left: '250px', height: '30px'}} />
                </Col>
                <Col flex="auto" style={{marginLeft: '30px'}}>
                    <Tabs activeKey={activeKey} onChange={setActiveKey} animated={false} className={marginTop(20)}>
                        <Tabs.TabPane tab={versionTabPaneContent} key="version" />
                        <Tabs.TabPane tab={codeTabPaneContent} key="code" />
                    </Tabs>
                </Col>
            </Row>

            {activeKey === 'version' && <CommitList compareMode={compareModeState} />}
            {activeKey === 'code' && toCommitId && fromCommitId && (
                <div className={marginTop(20)}>
                    <FileChange
                        key={`${from}...${to}`}
                        path={path}
                        toCommitId={toCommitId}
                        fromCommitId={fromCommitId}
                        onPathChange={handlePathChange}
                        compareMode={compareModeState}
                        mergeBaseCommitId={mergeBaseCommitId}
                    />
                </div>
            )}
        </>
    );
};

export default MergeDetail;
