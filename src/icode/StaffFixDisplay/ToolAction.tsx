import {Button} from '@panda-design/components';
import {useCallback, useMemo} from 'react';
import {last} from 'lodash';
import {getButtonVariantProps} from '@/components/Chat/Element/ToolActions/utils';
import {Link} from '@/links/createLink';
import {ChatAction} from '@/types/staff/element';
import {apiPostChatActionTrigger} from '@/api/staff';
import {setCurrentChatId, setCurrentChatOpen} from '@/regions/staff/chatSdk';
import {useCurrentChat} from '@/regions/staff/chat';
import {apiGetChatById} from '@/api/staff';
import {useCurrentConversationId} from './region';

interface Props {
    action: ChatAction;
    displayType: 'app' | 'overview';
    pipelineConfId: number;
}

const ToolAction = ({action, displayType, pipelineConfId}: Props) => {
    const {
        text,
        buttonType,
        href,
    } = action;
    const conversationId = useCurrentConversationId(pipelineConfId);
    const variantProps = useMemo(
        () => getButtonVariantProps(buttonType),
        [buttonType]
    );
    const currentChat = useCurrentChat();
    const handleClick = useCallback(
        async () => {
            setCurrentChatId(conversationId);
            const chat = await apiGetChatById({conversationId});
            const {messageIds, agentId} = chat ?? {};
            if (displayType === 'app' && action.actionTypes?.includes('openWindow')) {
                setCurrentChatOpen(true);
            }
            if (currentChat && action.callback) {
                apiPostChatActionTrigger({
                    agentId,
                    conversationId,
                    messageId: last(messageIds),
                    action,
                });
            }
        },
        [action, conversationId, currentChat, displayType]
    );
    const element = (
        <Button size="small" {...variantProps} onClick={handleClick}>
            {text}
        </Button>
    );
    if (href) {
        return (
            <Link blank to={href}>
                {element}
            </Link>
        );
    }
    return element;
};

export default ToolAction;

