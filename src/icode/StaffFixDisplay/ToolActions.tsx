import {Flex} from 'antd';
import {ActionElement} from '@/types/staff/element';
import ToolAction from './ToolAction';

interface Props {
    item: ActionElement;
    displayType: 'app' | 'overview';
    pipelineConfId: number;
}

const ToolActions = ({item, displayType, pipelineConfId}: Props) => {
    return (
        <Flex gap={8}>
            {item.actions?.map((action, index) => (
                <ToolAction pipelineConfId={pipelineConfId} displayType={displayType} action={action} key={index} />
            ))}
        </Flex>
    );
};

export default ToolActions;

