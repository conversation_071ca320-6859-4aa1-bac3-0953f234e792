import {ToolText} from '@/components/Chat/Element/ToolText';
import {ToolElement} from '@/types/staff/element';
import ToolActions from './ToolActions';


interface Props {
    item: ToolElement;
    displayType: 'app' | 'overview';
    pipelineConfId: number;
}

export default function Element({item, displayType, pipelineConfId}: Props) {
    switch (item?.type) {
        case 'text':
            return <ToolText item={item} />;
        case 'actions':
            return <ToolActions pipelineConfId={pipelineConfId} displayType={displayType} item={item} />;
    }
}
