import {Flex} from 'antd';
import {memo, useCallback, useEffect, useState} from 'react';
import {last} from 'lodash';
import {useDebouncedCallback} from 'huse';
import {staffSdk} from '@/utils/staff/staffSdk';
import {apiGetConversationByLabels} from '@/api/staff';
import {getChangeInfo, useCurrentPatchSets} from '@/regions/icode/review';
import StaffAvatar from '@/components/Chat/StaffAvatar';
import {useCurrentUserName} from '@/regions/icode/currentUser';
import {Timer} from '@/types/icode/common';
import {ToolElement} from '@/types/staff/element';
import {setCurrentChatId} from '@/regions/staff/chatSdk';
import {useChatIdEffect, useChatWebsocketEffect, useOpenChatEffect} from '@/components/Chat/hooks';
import Element from './Element';
import {
    setCurrentConversationId,
    useCurrentConversationId,
    setLatestConversation,
    useLatestConversation,
} from './region';

interface Props {
    displayType: 'app' | 'overview';
    pipelineConfId: number;
}

const StaffFixDisplay = ({displayType, pipelineConfId}: Props) => {
    const {changeNumber} = getChangeInfo();
    const {bPatchSet} = useCurrentPatchSets();
    const username = useCurrentUserName();
    const conversationId = useCurrentConversationId(pipelineConfId);
    const latestConversation = useLatestConversation({conversationId, pipelineConfId, displayType});
    const [, setTimer] = useState<Timer>();
    useChatIdEffect();
    useChatWebsocketEffect();
    useOpenChatEffect();

    const fetchConversation = useCallback(
        async () => {
            if (bPatchSet.patchSetId && changeNumber && pipelineConfId) {
                const result = await apiGetConversationByLabels({
                    labels: [`${changeNumber}_${bPatchSet.patchSetId}_${pipelineConfId}`],
                    username,
                    displayType,
                });
                const conversation = last(result);
                if (conversation) {
                    if (!conversationId) {
                        setCurrentConversationId(pipelineConfId, conversation.conversationId);
                    }
                    setLatestConversation({conversationId, pipelineConfId, displayType}, conversation);
                }
                else {
                    setTimer(
                        (timer: Timer) => {
                            clearTimeout(timer);
                            return setTimeout(() => {
                                fetchConversation();
                            }, 1000);
                        }
                    );
                }
            }

        },
        [bPatchSet.patchSetId, changeNumber, conversationId, displayType, pipelineConfId, username]
    );

    useEffect(
        () => {
            fetchConversation();
        },
        [fetchConversation]
    );
    const fetchConversationWidthDebounce = useDebouncedCallback(fetchConversation, 2000);

    useEffect(
        () => {
            if (conversationId) {
                setCurrentChatId(conversationId);
                staffSdk.addMessageListener(conversationId, () => {
                    fetchConversationWidthDebounce();
                });
            }
            return () => {
                setCurrentChatId(undefined);
            };
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [conversationId, displayType]
    );

    if (latestConversation?.noticeContent) {
        if (displayType === 'app') {
            return (
                <Flex align="center" gap={12}>
                    <StaffAvatar username="帝霸哥" agentId={latestConversation?.agentId} />
                    {latestConversation.noticeContent.elements.map((element: ToolElement, index) => (
                        <Element
                            pipelineConfId={pipelineConfId}
                            displayType={displayType}
                            key={index}
                            item={element}
                        />
                    ))}
                </Flex>
            );
        }
        else if (displayType === 'overview') {
            return (
                <Flex gap={12}>
                    <StaffAvatar username="帝霸哥" agentId={latestConversation?.agentId} />
                    <Flex vertical gap={12}>
                        {latestConversation.noticeContent.elements.map((element: ToolElement, index) => (
                            <Element
                                pipelineConfId={pipelineConfId}
                                displayType={displayType}
                                key={index}
                                item={element}
                            />
                        ))}
                    </Flex>
                </Flex>
            );
        }
    }
};

export default memo(StaffFixDisplay);
