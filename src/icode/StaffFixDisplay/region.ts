import {createMappedRegion} from 'region-core';
import {ConversationByLabel} from '@/types/staff/chat';

interface ConversationParam{
    conversationId: string;
    displayType?: 'app' | 'overview';
    pipelineConfId: number;
}

const latestConversationRegion = createMappedRegion<ConversationParam, ConversationByLabel>();
export const useLatestConversation = latestConversationRegion.useValue;
export const setLatestConversation = latestConversationRegion.set;
export const getLatestConversation = latestConversationRegion.getValue;


const currentConversationId = createMappedRegion<number, string>();
export const useCurrentConversationId = currentConversationId.useValue;
export const setCurrentConversationId = currentConversationId.set;
