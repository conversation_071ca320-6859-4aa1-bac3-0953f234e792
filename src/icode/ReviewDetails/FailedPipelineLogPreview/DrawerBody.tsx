import {useEffect} from 'react';
import {Result} from 'antd';
import {Button} from '@panda-design/components';
import {useRequest} from 'huse';
import {apiGetIPipeErrorMessage} from '@/api/icode/ipipePreview';
import {useChangeInfo} from '@/regions/icode/review';
import {useIPipeWorkspaceId} from '@/regions/icode/icloud';
import {useCurrentRepoName} from '@/regions/icode/currentRepo';
import {useReadErrorLogSetting} from '@/regions/icode/localStorage';
import {ErrorPage} from '@/design/icode/ErrorPages';
import StaffFixDisplay from '@/icode/StaffFixDisplay';
import TaskOverview from './TaskOverview';
import {VerticalSpacedContainer, StyledSpin} from './styled';

interface Props {
    stageId: number;
    pipelineBuildId: number;
    pipelineConfId: number;
    handlePreviewHide: () => void;
    setStageName: (name: string) => void;
}

// eslint-disable-next-line max-len
const FailedPipelineLogPreview = ({stageId, pipelineBuildId, pipelineConfId, handlePreviewHide, setStageName}: Props) => {
    const {changeNumber} = useChangeInfo();
    const lineNumBeforeOrAfter = useReadErrorLogSetting();
    const {pending, data, error} = useRequest(apiGetIPipeErrorMessage, {changeNumber, stageId, lineNumBeforeOrAfter});
    const repoName = useCurrentRepoName();
    const ipipeWorkspaceId = useIPipeWorkspaceId(repoName);
    const {pluginErrorMessages = []} = data ?? {};

    useEffect(
        () => {
            const {stageName = ''} = data ?? {};
            if (stageName) {
                setStageName(stageName);
            }
        },
        [data, setStageName]
    );

    if (pending) {
        return <StyledSpin tip="加载中，请稍后..." />;
    }
    if (error) {
        return <ErrorPage error={error} />;
    }
    if (data) {
        return (
            <VerticalSpacedContainer size={32}>
                {pluginErrorMessages.map((plugin, idx) => (
                    <TaskOverview
                        key={plugin.jobId}
                        sequence={idx + 1}
                        plugin={plugin}
                        workspaceId={ipipeWorkspaceId}
                        buildId={pipelineBuildId}
                        stageId={stageId}
                    />
                ))}
                <StaffFixDisplay pipelineConfId={pipelineConfId} displayType="overview" />
            </VerticalSpacedContainer>
        );
    }
    return (
        <Result
            subTitle="暂不支持展示此类型的流水线日志，如有问题请联系zhangnanxi"
            status="warning"
            extra={<Button type="primary" onClick={handlePreviewHide}>关闭窗口</Button>}
        />
    );
};

export default FailedPipelineLogPreview;
