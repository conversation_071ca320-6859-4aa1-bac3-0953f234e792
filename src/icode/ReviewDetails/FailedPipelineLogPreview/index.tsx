import {useCallback, useMemo, useRef, useState} from 'react';
import {Button} from '@panda-design/components';
import {useBoolean} from 'huse';
import {useChangeInfo} from '@/regions/icode/review';
import {ChangeInfoPipeline} from '@/types/icode/review';
import {useCurrentRepoName} from '@/regions/icode/currentRepo';
import {track} from '@/utils/icode/track';
import {StyledDrawer} from './styled';
import DrawerTitle from './DrawerTitle';
import DrawerBody from './DrawerBody';

interface Props {
    pipeline: ChangeInfoPipeline;
}

const FailedPipelineLogPreview = ({pipeline}: Props) => {
    const startTimeRef = useRef<number>();
    const [visible, {on: showPreview, off: hidePreview}] = useBoolean();
    const [stageName, setStageName] = useState('');
    const {changeNumber} = useChangeInfo();
    const repoName = useCurrentRepoName();
    const {stageId, pipelineUrl, pipelineConfId, pipelineBuildId, stageCount, currentStageNum} = pipeline;

    const handleClick = useCallback(
        () => {
            showPreview();
            startTimeRef.current = Date.now();
            track('新评审页', '预览失败流水线日志', {stageId, changeNumber, repoName});
        },
        [changeNumber, repoName, showPreview, stageId]
    );

    const handlePreviewHide = useCallback(
        () => {
            if (startTimeRef.current) {
                const duration = Date.now() - startTimeRef.current;
                track('新评审页', '预览失败流水线日志时长', {stageId, duration, changeNumber, repoName});
            }
            hidePreview();
        },
        [changeNumber, hidePreview, repoName, stageId]
    );

    const title = useMemo(
        () => (
            stageName ? (
                <DrawerTitle
                    href={pipelineUrl}
                    title={`(${currentStageNum}/${stageCount}) ${stageName}`}
                    tooltip={`共${stageCount}个阶段，当前在第${currentStageNum}阶段`}
                />
            ) : null
        ),
        [currentStageNum, pipelineUrl, stageCount, stageName]
    );

    return (
        <>
            <Button
                type="primary"
                size="small"
                onClick={handleClick}
            >
                错误日志概览
            </Button>
            <StyledDrawer
                title={title}
                placement="right"
                visible={visible}
                onClose={handlePreviewHide}
                width={850}
                headerStyle={{position: 'sticky', top: 0}}
            >
                <DrawerBody
                    pipelineConfId={pipelineConfId}
                    stageId={stageId}
                    pipelineBuildId={pipelineBuildId}
                    handlePreviewHide={handlePreviewHide}
                    setStageName={setStageName}
                />
            </StyledDrawer>
        </>
    );
};

export default FailedPipelineLogPreview;
