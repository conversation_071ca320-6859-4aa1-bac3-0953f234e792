import styled from '@emotion/styled';
import {Drawer, Spin} from 'antd';
import {IconSkip} from '@/icons-icode/actions';

interface VerticalSpacedContainerProps {
    size?: number;
}

export const VerticalSpacedContainer = styled.div<VerticalSpacedContainerProps>`
    display: flex;
    flex-direction: column;
    gap: ${({size}) => size ?? 12}px;
    overflow-y: auto;
`;

export const IconExternal = styled(IconSkip)`
    min-width: 12px;
    width: 12px;
    height: 12px;
`;

export const StyledDrawer = styled(Drawer)`
    .ant-drawer-header-title,
    .ant-drawer-title {
        width: 100%;
    }
    .ant-drawer-body {
        padding-top: 10px;
    }
`;

export const StyledSpin = styled(Spin)`
    width: 100%;
    margin-top: 40px;
`;
