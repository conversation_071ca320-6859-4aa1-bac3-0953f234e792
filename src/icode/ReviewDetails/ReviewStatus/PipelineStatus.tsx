/**
 * @file 持续集成状态
 */

import {Flex, Space} from 'antd';
import {useRequestCallback} from 'huse';
import {css} from '@emotion/css';
import {useEffect, useMemo} from 'react';
import {Text} from '@panda-design/components';
import {ChangeInfoPipeline} from '@/types/icode/review';
import {IPipeAsyncPipelineLink} from '@/components/icode/IPipeAsyncLink';
import {useCurrentRepoName} from '@/regions/icode/currentRepo';
import {track} from '@/utils/icode/track';
import {apiGetAppIdByIpipeId} from '@/api/icode/review';
import ReviewDeploymentList from '@/components/icode/ReviewDeploymentList';
import StaffFixDisplay from '@/icode/StaffFixDisplay';
import PipelineStatusDetail from '../ReviewStatusDetail/PipelineStatusDetail';
import FailedPipelineLogPreview from '../FailedPipelineLogPreview';
import Status from './Status';
import CIStatus from './CIStatus';
import TriggerPipelineButton from './TriggerPipelineButton';

const linkCss = css`
    overflow: hidden;
    white-space: nowrap;
    display: block;
`;

interface PipelineStatusProps {
    pipeline: ChangeInfoPipeline;
}

interface PipeLinkProps extends PipelineStatusProps {
    onClick?: () => void;
}

const PipeLink = ({onClick, pipeline}: PipeLinkProps) => {
    const repoName = useCurrentRepoName();
    const {pipelineName, repoPath} = pipeline;
    const pipelineUrl = pipeline.pipelineUrl ?? '';
    let repoText = '';
    if (repoPath && repoPath !== repoName) {
        repoText = ` (代码库: ${repoPath})`;
    }

    const pipelineFullName = `持续集成-${pipelineName}${repoText}`;

    return (
        <IPipeAsyncPipelineLink
            className={linkCss}
            pipelineUrl={pipelineUrl}
            onClick={onClick}
        >
            <Text
                ellipsis={{tooltip: pipelineFullName}}
                style={{maxWidth: 300, color: 'var(--color-brand-6)', fontSize: 12, verticalAlign: 'middle'}}
            >
                {pipelineFullName}
            </Text>
        </IPipeAsyncPipelineLink>
    );
};

const PipelineStatus = ({pipeline}: PipelineStatusProps) => {
    const {
        currentStageNum,
        pipelineUrl,
        result,
        stageCount,
        isPipelineExempted,
        isBlock,
        pipelineBuildId,
        pipelineConfId,
    } = pipeline;
    const requestParam = useMemo(
        () => ({ipipeId: pipelineConfId}),
        [pipelineConfId]
    );
    const [request, res] = useRequestCallback(apiGetAppIdByIpipeId, requestParam);

    useEffect(
        () => {
            if (requestParam.ipipeId) {
                request();
            }
        },
        [request, requestParam]
    );

    if (isPipelineExempted) {
        return (
            <Status type={'SUCCESS'} title={'持续集成'}>
                <CIStatus />
            </Status>
        );
    }

    const pipelineResult = result === 'SUCCESS'
        ? (currentStageNum === stageCount ? 'SUCCESS' : 'STAGE')
        : result;

    return (
        <Flex vertical gap={4}>
            <Status
                block={isBlock}
                key={pipelineUrl}
                type={pipelineResult}
                title={(
                    <PipeLink
                        pipeline={pipeline}
                        onClick={() => track('新评审页', `跳转流水线-${pipelineResult}`)}
                    />
                )}
            >
                {res.data?.applicationId
                    ? <ReviewDeploymentList appId={res.data?.applicationId} buildId={pipelineBuildId} />
                    : ''}
                <Space size={8}>
                    <PipelineStatusDetail pipeline={pipeline} />
                    {pipelineResult === 'FAILED' && <FailedPipelineLogPreview pipeline={pipeline} />}
                    <TriggerPipelineButton
                        text="快照执行"
                        buildType="SNAPSHOT"
                        pipeline={pipeline}
                        tooltipTitle="使用「当前 CR 最新 patch」重新触发流水线，基础库版本为「当次执行的commit」"
                    />
                    <TriggerPipelineButton
                        text="全新执行"
                        buildType="NEW"
                        pipeline={pipeline}
                        tooltipTitle="使用「当前 CR 最新 patch」重新触发流水线，基础库版本为「最新提交的commit」"
                    />
                </Space>
            </Status>
            {result === 'FAILED' && (
                <StaffFixDisplay
                    key={pipelineConfId}
                    pipelineConfId={pipelineConfId}
                    displayType="app"
                />
            )}
        </Flex>
    );
};

export default PipelineStatus;
