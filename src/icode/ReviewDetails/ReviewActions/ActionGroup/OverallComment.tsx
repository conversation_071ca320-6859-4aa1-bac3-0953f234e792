/* eslint-disable max-len */
import {useMemo, useCallback} from 'react';
import {Button, message} from '@panda-design/components';
import {useBoolean} from 'huse';
import {isEmpty, get, isString} from 'lodash';
import {useChatSendMessage} from '@baidu/k-guest';
import styled from '@emotion/styled';
import {useChangeInfo} from '@/regions/icode/review';
import useReviewTrack from '@/hooks/icode/review/useReviewTrack';
import klogo from '@/assets/icode/klogo.png';
import {useCurrentRepoName} from '@/regions/icode/currentRepo';
import {usePatchSetList, useAPatchSetCommitId, useLatestPatch} from '@/regions/icode/review';
import {useDefaultIntelligentReview} from '@/regions/icode/reviewSettings';
import {getFileIntelligentReviewResult, loadFileIntelligentReviewResult} from '@/regions/icode/review/intelligentReview';
import {intelligentColors} from '@/constants/colors/icode';

const OverallButton = styled(Button)`
    display: flex;
    align-items: center;
    font-size: 14px !important;
    color: ${intelligentColors.reviewButtonColor} !important;
    border-color: ${intelligentColors.reviewButtonColor} !important;
`;

const StyledImg = styled.img`
    width: 16px;
    height: 16px;
    margin-right: 8px;
`;

const ActionGroup = () => {
    const sendMessage = useChatSendMessage();
    const repo = useCurrentRepoName();
    const {changeNumber, subject, changeStatus} = useChangeInfo();
    const patchSetList = usePatchSetList();
    const baseCommit = useAPatchSetCommitId();
    const latestPatch = useLatestPatch();
    const [loading, {on: showLoading, off: hideLoading}] = useBoolean(false);
    const reviewTrack = useReviewTrack();
    const defaultIntelligentReview = useDefaultIntelligentReview();

    const disabled = useMemo(
        () => isEmpty(patchSetList),
        [patchSetList]
    );

    const handleOnClick = useCallback(
        async () => {
            reviewTrack('智能评审', {placeOfUse: 'review', intelligentReviewAction: '点击生成按钮'});
            showLoading();
            try {
                const params = {
                    repo,
                    changeNumber,
                    aCommitId: baseCommit,
                    bCommitId: get(latestPatch, 'commitId', ''),
                };
                await loadFileIntelligentReviewResult(params);
                const overallComment = getFileIntelligentReviewResult(params);
                if (isString(overallComment) && overallComment) {
                    reviewTrack('智能评审', {placeOfUse: 'review', intelligentReviewAction: '生成评论', adopt: '已采纳'});
                    sendMessage([
                        {
                            role: 'user',
                            payload: `评审 ${subject} 的变更总结`,
                            triggerRequest: false,
                            source: 'k_chat_code_review_summary',
                        },
                        {
                            role: 'assistant',
                            payload: {data: {content: overallComment}},
                            source: 'k_chat_code_review_summary',
                        },
                    ]);
                    hideLoading();
                }
                else {
                    hideLoading();
                    message.info('这块代码写的很棒，小码哥没找到问题～');
                }
            }
            catch (e) {
                hideLoading();
                message.error(e.message);
            }
        },
        [baseCommit, changeNumber, hideLoading, latestPatch, repo, reviewTrack, sendMessage, showLoading, subject]
    );

    if (!defaultIntelligentReview || ['ABANDONED', 'MERGED'].includes(changeStatus)) {
        return null;
    }

    return (
        <OverallButton disabled={disabled} onClick={handleOnClick} loading={loading}>
            {!loading && <StyledImg src={klogo} />}
            变更摘要
        </OverallButton>
    );
};

export default ActionGroup;
