import {createRegion} from 'region-core';
import {MergeCompareInfo} from '@/types/icode/repo';
import {apiGetMergeCompareInfo, apiGetMergeBase} from '@/api/icode/merge';

const mergeCompareInfoRegion = createRegion<MergeCompareInfo>();

export const loadMergeCompareInfo = mergeCompareInfoRegion.loadBy(apiGetMergeCompareInfo);

export const useMergeCompareInfoLoading = mergeCompareInfoRegion.useLoading;

export const useMergeCompareInfo = mergeCompareInfoRegion.useValue;

export const useMergeCompareInfoError = mergeCompareInfoRegion.useError;

// merge-base region
const mergeBaseRegion = createRegion<string>();

export const loadMergeBase = mergeBaseRegion.loadBy(apiGetMergeBase);

export const useMergeBaseLoading = mergeBaseRegion.useLoading;

export const useMergeBase = mergeBaseRegion.useValue;

export const useMergeBaseError = mergeBaseRegion.useError;

