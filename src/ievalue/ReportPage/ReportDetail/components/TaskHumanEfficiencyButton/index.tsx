import {LineChartOutlined} from '@ant-design/icons';
import {Button} from '@panda-design/components';
import {Modal} from '@panda-design/components';
import {useBoolean} from 'huse';
import {CustomBoundary} from '@/components/ievalue/Boundary';
import TaskHumanEfficiencyContent from './TaskHumanEfficiencyContent';

export const TaskHumanEfficiencyButton = () => {
    const [isButtonsVisible, {on: show, off: hide}] = useBoolean(false);
    return (
        <>
            <Button type="link" icon={<LineChartOutlined />} onClick={show}>
                人效统计
            </Button>
            <Modal
                title="人效统计"
                open={isButtonsVisible}
                onCancel={hide}
                footer={null}
                destroyOnHidden
                width={1100}
                centered
                maskClosable={false}
            >
                <CustomBoundary.Loading>
                    <TaskHumanEfficiencyContent />
                </CustomBoundary.Loading>
            </Modal>
        </>
    );
};
