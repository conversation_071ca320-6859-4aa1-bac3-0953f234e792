import {Flex, Form, Select} from 'antd';
import {useMemo, useState} from 'react';
import ReactEchart from 'echarts-for-react';
import {useRequest} from 'huse';
import {apiEvaluateDashboardHumanEfficiency} from '@/api/ievalue/report';
import {useTaskStageList, useTaskTaskID} from '@/hooks/ievalue/task';
import {TaskStageEnum} from '@/constants/ievalue/task';

const TimeOptions = [1, 6, 12, 24].map(item => ({
    label: item,
    value: item,
}));

const TaskHumanEfficiencyChart = () => {
    const taskID = useTaskTaskID();
    const [stageList] = useTaskStageList();
    const stageOptions = useMemo(
        () => {
            return stageList?.filter(i =>
                [
                    TaskStageEnum.EVALUATING,
                    TaskStageEnum.AUDITING,
                    TaskStageEnum.AUDITING_FORWARD,
                    TaskStageEnum.ACCEPTING,
                ].includes(i.stageType)
            );
        },
        [stageList]
    );
    const [hourGap, setHourGap] = useState(6);
    const [stageIDs, setStageIDs] = useState(stageOptions?.map(i => i.ID));

    const {data, pending} = useRequest(apiEvaluateDashboardHumanEfficiency, {
        taskID,
        stageIDs,
        hourGap,
    });

    const [barChartOption, chartKey] = useMemo(
        () => {
            const option = {
                legend: {
                    data: data?.yData?.map(item => item.name) || [],
                },
                tooltip: {
                    trigger: 'axis',
                },
                xAxis: {
                    type: 'time',
                    minInterval: 1000 * 60 * 60,
                    axisLabel: {
                        formatter: {
                            year: '{yyyy}年',
                            month: '{MMM}月',
                            day: '{d}日',
                            hour: '{HH}:{mm}',
                            minute: '{HH}:{mm}',
                        },
                    },
                },
                yAxis: {
                    type: 'value',
                },
                series:
                data?.yData?.map(item => {
                    return {
                        name: item.name,
                        type: 'line',
                        data: item?.numberList?.map((item, index) => [
                            data?.xData[index],
                            item,
                        ]),
                    };
                }) || [],
            };
            return [option, Date.now()];
        },
        [data]
    );

    return (
        <Flex vertical>
            <Flex gap={8}>
                <Form.Item label="阶段">
                    <Select
                        options={stageOptions}
                        fieldNames={{label: 'stageName', value: 'ID'}}
                        style={{width: 500}}
                        mode="multiple"
                        value={stageIDs}
                        onChange={setStageIDs}
                    />
                </Form.Item>
                <Form.Item label="时间间隔（小时）">
                    <Select
                        options={TimeOptions}
                        style={{width: 90}}
                        onChange={setHourGap}
                        value={hourGap}
                    />
                </Form.Item>
            </Flex>
            <Flex justify="center" align="center">
                <ReactEchart
                    key={chartKey}
                    option={barChartOption}
                    style={{height: 600, width: '100%'}}
                    showLoading={pending}
                />
            </Flex>
        </Flex>
    );
};

export default TaskHumanEfficiencyChart;
