import {useMemo} from 'react';
import {useTaskStageList} from '@/hooks/ievalue/task';
import {TaskStageEnum} from '@/constants/ievalue/task';
import TaskHumanEfficiencyChart from './TaskHumanEfficiencyChart';

const TaskHumanEfficiencyContent = () => {
    const [stageList] = useTaskStageList();
    const hasStage = useMemo(
        () => {
            return stageList?.some(i =>
                [
                    TaskStageEnum.EVALUATING,
                    TaskStageEnum.AUDITING,
                    TaskStageEnum.AUDITING_FORWARD,
                    TaskStageEnum.ACCEPTING,
                ].includes(i.stageType)
            );
        },
        [stageList]
    );

    return hasStage ? (
        <TaskHumanEfficiencyChart />
    ) : (
        <span>暂无可统计阶段</span>
    );
};

export default TaskHumanEfficiencyContent;
