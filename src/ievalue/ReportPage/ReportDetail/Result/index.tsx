import {Typography} from 'antd';
import {IDashboardDetail, IEvaluateDetail} from '@/api/ievalue/report';
import {TaskInfoItem} from '@/api/ievalue/task';
import {CustomBoundary} from '@/components/ievalue/Boundary';
import {FlexLayout} from '@/components/ievalue/FlexLayout';
import {TaskHumanEfficiencyButton} from '../components/TaskHumanEfficiencyButton';
import StatisticalInfo from './StatisticalInfo';
import StatisticalTabs from './StatisticalTabs';
import styles from './index.module.less';
interface IProps {
    data?: IDashboardDetail;
    taskInfo?: TaskInfoItem;
    detail?: IEvaluateDetail;
}
export default (props: IProps) => {
    return (
        <div className={styles.container}>
            <FlexLayout justify="space-between">
                <Typography.Title level={4} style={{marginBottom: 16}}>
                    评估结果
                </Typography.Title>
                <TaskHumanEfficiencyButton />
            </FlexLayout>
            <StatisticalInfo {...props} />
            <CustomBoundary.Loading>
                <StatisticalTabs {...props} />
            </CustomBoundary.Loading>
        </div>
    );
};
