import {Form, Switch} from 'antd';
import {TargetEnum} from '@/constants/ievalue/task';
import {useCreateTaskIsOnlyPREDICTING, useCreateTaskIsAutoEvaluate} from '../../CreateTaskProvider/DatasetProvider';
import {useCreateTaskIsQuickEvaluate, useCreateTaskTarget} from '../../CreateTaskProvider/CreateTaskBaseProvider';


export const BlindFormItem = () => {
    const onlyPREDICTING = useCreateTaskIsOnlyPREDICTING();
    const isAutoEvaluate = useCreateTaskIsAutoEvaluate();
    const isQuickEvaluate = useCreateTaskIsQuickEvaluate();
    const target = useCreateTaskTarget();

    return !isAutoEvaluate
        && !isQuickEvaluate
        && ![TargetEnum.Prompt, TargetEnum.PromptFlow, TargetEnum.Petite].includes(target) ? (
            <Form.Item
                label="盲评"
                name="blind"
                hidden={onlyPREDICTING}
                valuePropName="checked"
                tooltip="当业务需要最大限度的规避掉主观好恶对评估公正性的影响，可以开启盲评。盲评时将无法查看模型名称。盲评的同时，不同的题目之间，还会对结果顺序进行打乱，进一步降低识破的可能性；"
            >
                <Switch />
            </Form.Item>
        ) : (
            <></>
        );
};
