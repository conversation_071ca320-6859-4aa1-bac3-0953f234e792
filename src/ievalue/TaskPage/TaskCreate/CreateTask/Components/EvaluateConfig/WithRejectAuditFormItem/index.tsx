import {Form, Switch} from 'antd';
import {TaskStageEnum} from '@/constants/ievalue/task';
import {RejectRatioParamItem} from '@/api/ievalue/task';
import {useCreateTaskTemplateStageTypes} from '../../CreateTaskProvider/DatasetProvider';
import {
    useCreateTaskTarget,
    useCreateTaskIsQuickEvaluate,
} from '../../CreateTaskProvider/CreateTaskBaseProvider';
import WithRejectParam from './WithRejectParam';

export const WithRejectAuditFormItem = () => {
    const target = useCreateTaskTarget();
    const form = Form.useFormInstance();
    const isQuickEvaluate = useCreateTaskIsQuickEvaluate();
    const isWithReject = Form.useWatch('withReject', form);
    const dispatchType = Form.useWatch('dispatchType', form);
    const stageTypes = useCreateTaskTemplateStageTypes();

    return stageTypes?.includes(TaskStageEnum.AUDITING_FORWARD)
        && !isQuickEvaluate
        && !!dispatchType
        && target !== 'Petite' ? (
            <>
                <Form.Item
                    label="批量打回"
                    name="withReject"
                    valuePropName="checked"
                    tooltip="开启后，准确率低于设定阈值将触发批量打回弹窗，支持将被审核人其余待审核用例打回重做。"
                >
                    <Switch />
                </Form.Item>
                {isWithReject ? (
                    <Form.Item
                        label="准确率阈值"
                        name="rejectRateParams"
                        rules={[
                            {
                                validator: async (
                                    _,
                                    valueList: RejectRatioParamItem[]
                                ) => {
                                    if (
                                        valueList.some(item => !item.rejectRatio)
                                    ) {
                                        return Promise.reject(
                                            new Error('打回比例不能为空')
                                        );
                                    }
                                    return Promise.resolve();
                                },
                            },
                        ]}
                    >
                        <WithRejectParam />
                    </Form.Item>
                ) : (
                    <></>
                )}
            </>
        ) : (
            <></>
        );
};
