import {Form, Switch} from 'antd';
import {TargetEnum} from '@/constants/ievalue/task';
import {
    useCreateTaskIsOnlyPREDICTING,
    useCreateTaskIsAutoEvaluate,
} from '../../CreateTaskProvider/DatasetProvider';
import {
    useCreateTaskIsQuickEvaluate,
    useCreateTaskTarget,
} from '../../CreateTaskProvider/CreateTaskBaseProvider';

export const HideOperatorFormItem = () => {
    const onlyPREDICTING = useCreateTaskIsOnlyPREDICTING();
    const isAutoEvaluate = useCreateTaskIsAutoEvaluate();
    const isQuickEvaluate = useCreateTaskIsQuickEvaluate();
    const target = useCreateTaskTarget();

    return !isAutoEvaluate
        && !isQuickEvaluate
        && ![TargetEnum.PromptFlow, TargetEnum.Petite].includes(target) ? (
            <Form.Item
                label="隐藏操作人信息"
                name="hideOperator"
                hidden={onlyPREDICTING}
                valuePropName="checked"
                tooltip="开启后评估任务各阶段打分操作人的信息将被隐藏，仅管理员和任务创建人可以点击查看"
            >
                <Switch />
            </Form.Item>
        ) : (
            <></>
        );
};
