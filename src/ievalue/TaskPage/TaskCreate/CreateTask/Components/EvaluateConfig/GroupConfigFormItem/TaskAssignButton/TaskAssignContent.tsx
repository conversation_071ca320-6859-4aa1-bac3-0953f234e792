import {
    Form,
    FormInstance,
    Select,
    Switch,
    Typography,
} from 'antd';
import {TeamOutlined} from '@ant-design/icons';
import {Button} from '@panda-design/components';
import {useCallback, useMemo} from 'react';
import {uniq} from 'lodash';
import {useRequest} from 'huse';
import {
    DatasetCaseDispatchItem,
    apiDatasetCaseDispatch,
    apiDatasetL1,
} from '@/api/ievalue/task';
import {FlexLayout} from '@/components/ievalue/FlexLayout';
import {SpaceUserGroupListItem} from '@/api/ievalue/space';
import {getAssignedInfo} from '../CaseAssignTable';
import {GroupUserSelect} from '../GroupUserSelect';
import {TaskAssignSelectTable} from './TaskAssignSelectTable';

interface TaskAssignContentProps {
    form: FormInstance;
    onFinish: () => void;
    stageList: DatasetCaseDispatchItem[];
    datasetID: number;
    templateID: number;
}

const TaskAssignContent = ({
    onFinish,
    stageList,
    datasetID,
    templateID,
    form,
}: TaskAssignContentProps) => {
    const {data: datasetL1} = useRequest(apiDatasetL1, {
        datasetID,
        templateID,
        levelKey: 'l0',
    });
    const handleFinish = useCallback(
        async (values: any) => {
            try {
                const groups = values.groupUserList?.filter((e: SpaceUserGroupListItem) => !!e.groupID) || [];
                const userList = values.groupUserList?.filter((e: any) => !!e.username) || [];
                const groupUserList: string[] = [];
                groups.forEach((g: SpaceUserGroupListItem) => {
                    groupUserList.push(...g.usernameList);
                });
                const users = uniq([...userList.map((e: any) => e.username), ...groupUserList]);
                const stage = stageList?.find(e => e.sequence === values.sequence);
                const params = [
                    {
                        stageType: stage?.stageType,
                        sequence: values.sequence,
                        username: users,
                    },
                ];
                await apiDatasetCaseDispatch({
                    datasetID,
                    templateID,
                    params,
                    caseIDs: values.caseIDs,
                    allowUserRepeat: values.allowUserRepeat,
                    allowCaseRepeat: true,
                });
                onFinish();
                form.resetFields();
            } catch (error) {
                /* empty */
            }
        },
        [datasetID, form, onFinish, stageList, templateID]
    );
    const l0Options = useMemo(
        () => {
            return datasetL1?.list?.map(e => {
                return {
                    value: e,
                    label: e,
                };
            });
        },
        [datasetL1]
    );
    const caseIDs = Form.useWatch('caseIDs', form);
    const groupUserList = Form.useWatch('groupUserList', form);

    const tip = useMemo(
        () => {
            return stageList.map(item => {
                const {count} = getAssignedInfo(item.dispatch);
                return (
                    <Typography.Text
                        key={item.stageType}
                    >{`${item.stageName}阶段：`}
                        <span style={{color: '#52c41a'}}>{count}</span>
                        {`/${item.totalNum}`}
                    </Typography.Text>
                );
            });
        },
        [stageList]
    );
    const [disabled, disabledReason] = useMemo(
        () => {
            let disabled = false;
            let disabledReason = '';
            if (!groupUserList?.length) {
                disabled = true;
                disabledReason = '请选择分配人员或用户组';
            }
            if (!caseIDs?.length) {
                disabled = true;
                disabledReason = '请选择分配case';
            }
            return [disabled, disabledReason];
        },
        [caseIDs?.length, groupUserList?.length]
    );
    return (
        <Form
            form={form}
            onFinish={handleFinish}
            initialValues={{
                sequence: stageList[0]?.sequence,
                allowUserRepeat: true,
            }}
            labelAlign="left"
            autoComplete="off"
            labelWrap
        >
            <Form.Item
                name="allowUserRepeat"
                label="允许人员重复分配"
                style={{width: '100%'}}
                valuePropName="checked"
            >
                <Switch />
            </Form.Item>
            <Form.Item name={'sequence'} label="分配阶段" required>
                <Select
                    options={stageList}
                    fieldNames={{
                        value: 'sequence',
                        label: 'stageName',
                    }}
                    style={{width: 90}}
                />
            </Form.Item>
            <Form.Item
                label="选择case并指定负责人"
                style={{width: '100%'}}
                required
            >
                <FlexLayout gap={12}>
                    <Form.Item name="groupUserList" noStyle>
                        <GroupUserSelect />
                    </Form.Item>
                    <Button
                        type="primary"
                        htmlType="submit"
                        icon={<TeamOutlined />}
                        disabledReason={disabledReason}
                        disabled={disabled}
                    >
                        分配
                    </Button>
                </FlexLayout>
            </Form.Item>
            <FlexLayout gap={12} style={{marginBottom: 12}}>
                {tip}
            </FlexLayout>
            <FlexLayout
                style={{
                    padding: '12px 14px',
                    width: '100%',
                    background: '#f5f5f5',
                }}
            >
                <FlexLayout
                    style={{
                        height: 34,
                        width: 60,
                        color: 'var(--color-gray-8)',
                    }}
                    align="center"
                >
                    Case筛选
                </FlexLayout>
                <Form.Item name="l0" label="L0" style={{marginBottom: 0}}>
                    <Select
                        options={l0Options}
                        style={{width: 380}}
                        mode="multiple"
                        placeholder="请输选择分类"
                    />
                </Form.Item>
            </FlexLayout>

            <Form.Item name="caseIDs">
                <TaskAssignSelectTable
                    stageList={stageList}
                    datasetID={datasetID}
                    templateID={templateID}
                />
            </Form.Item>
        </Form>
    );
};

export default TaskAssignContent;
