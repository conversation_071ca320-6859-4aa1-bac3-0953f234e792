/* eslint-disable max-lines */
/* eslint-disable complexity */
import {CopyOutlined} from '@ant-design/icons';
import {createIcon} from '@panda-design/components';
import {Tooltip, Typography} from 'antd';
import {useCallback, useEffect, useRef, useState} from 'react';
import {Boundary} from 'react-suspense-boundary';
import {default as RobotIcon} from '@/assets/ievalue/ChatRobot';
import RecordIcon from '@/assets/ievalue/Record';
import {Markdown} from '@/design/Markdown';
import {ChatRecordItem} from '@/api/ievalue/case';
import {PromptExcuteResultListItem} from '@/api/ievalue/prompt-version';
import {TaskModelItem} from '@/api/ievalue/task';
import {Expend} from '@/components/ievalue/Expend';
import {FlexLayout} from '@/components/ievalue/FlexLayout';
import LoadingText from '@/components/ievalue/LoadingText';
import {ChatStatusEnum} from '@/constants/ievalue/case';
import {useChatState, useFindModel, useTaskInfo} from '@/hooks/ievalue/task';
import JsonOrMarkdownOrDiff from '@/components/ievalue/JsonOrMarkdownOrDiff';
import {ChatRecordEditorTypeEnum} from '@/regions/ievalue/task/chatRecordEditModalRegion';
import {useChatOneClickExpand} from '@/providers/TaskDetailProviders/ChatExtendInfoProvider';
import {MDChangeButton} from '@/components/ievalue/MDChangeButton';
import {ReasonContentButton} from '@/components/Evaluate/ModalCompare/ReasonContentButton';
import {ChatScratchWords} from '@/components/ievalue/ScratchWordsMarkdown';
import {SearchResults} from '@/components/ievalue/SearchResults';
import {debounceRefreshScratchWords} from '@/components/ievalue/ScratchWordsMarkdown/ScratchWordsProvider';
import DeleteRecordButton from
    '../../../../TaskGroupCase/components/EditChatCaseButton/DeleteRecordButton';
import {AgentRecordButton} from '../../../Chat/MsgList/AgentRecordButton';
import AudioLinkButton from '../../../Chat/MsgList/Item/AudioLinkButton';
import MoreInfoButton from '../../../Chat/MsgList/Item/MoreInfoButton';
import OptionalRawButton from '../../../Chat/MsgList/Item/OptionalRawButton';
import styles from '../../../Chat/MsgList/Item/index.module.less';
import {MsgItemScoreItem} from '../MsgItemScoreItem';
import ChatRecordEditorButton from '../../../ChatRecordEditorButton';
import {OriginCotent} from '../../../Chat/MsgList/Item';
import {MultiModalButton} from '../../../Chat/MsgList/MultiModalButton';
export const IconRobot = createIcon(RobotIcon);
interface IChatRecordItemPorps {
    item: ChatRecordItem;
    index?: number;
    data?: PromptExcuteResultListItem;
    openScore?: boolean;
    setOpenScore?: (open: boolean) => void;
    refresh?: () => void;
    getPopupContainer?: () => HTMLElement;
    showOrigion?: boolean;
    setShowOrigion?: (show: boolean) => void;
    isFullScreen?: boolean;
}

const ToolComp = ({
    item,
    data,
    onChangeScore,
    refresh,
    showOrigion,
    setShowOrigion,
}: IChatRecordItemPorps & {
    onChangeScore: () => void;
}) => {
    const [taskInfo] = useTaskInfo();
    const model = useFindModel(
        (model: TaskModelItem) => model.modelID === data?.modelID
    );
    return (
        <FlexLayout
            gap={8}
            justify="end"
            align="center"
            style={{width: '100%'}}
        >
            <MDChangeButton
                showOrigion={showOrigion}
                setShowOrigion={setShowOrigion}
            />
            {model?.modelType.includes('灵境') && (
                <AgentRecordButton
                    chatTaskID={data?.chatTaskID}
                    predictRecordID={item?.predictRecordID}
                    chatRecordID={item?.ID}
                />
            )}
            <Typography.Text
                copyable={{
                    text: item?.output || '',
                    tooltips: '复制对话',
                    icon: (
                        <CopyOutlined
                            style={{
                                color: '#000',
                                width: '14px',
                                height: '14px',
                            }}
                        />
                    ),
                }}
            />
            {taskInfo.stage !== 'TERMINATED' && (
                <>
                    <ChatRecordEditorButton
                        item={item}
                        type={ChatRecordEditorTypeEnum.Output}
                        onRefresh={refresh}
                    />
                    {!!taskInfo?.isRoundScoring && (
                        <Tooltip title="满意度评分">
                            <RecordIcon
                                onClick={onChangeScore}
                                style={{
                                    color: item.scoreUnChecked
                                        ? 'rgba(0, 0, 0, 0.88)'
                                        : '#2d70ff',
                                }}
                            />
                        </Tooltip>
                    )}
                </>
            )}
            <MoreInfoButton item={item} />
            <OptionalRawButton item={item} />
            {!!item?.audioLink && (
                <AudioLinkButton audioLink={item?.audioLink} />
            )}
        </FlexLayout>
    );
};

const BotContentComp = (props: IChatRecordItemPorps) => {
    const {setOpenScore, item, openScore, data, isFullScreen, refresh} = props;
    const [showOrigion, setShowOrigion] = useState(false);
    const onChangeScore = useCallback(
        () => {
            setOpenScore?.(!openScore);
        },
        [openScore, setOpenScore]
    );
    useEffect(
        () => {
            debounceRefreshScratchWords();
        },
        [isFullScreen]
    );
    const {statusItem} = useChatState(data?.modelID || 0);
    if (
        statusItem?.status === ChatStatusEnum.ChatRunning
        && item?.output === ''
    ) {
        return <LoadingText text="正生成回答，请耐心等候..." />;
    }

    return (
        <div style={{overflow: 'hidden', padding: '0 8px', width: '100%'}}>
            <ToolComp
                {...props}
                onChangeScore={onChangeScore}
                showOrigion={showOrigion}
                setShowOrigion={setShowOrigion}
            />
            <ReasonContentButton reasoningContent={item?.reasoningContent} />
            {showOrigion ? (
                <OriginCotent>{item?.output || ''}</OriginCotent>
            ) : (
                <ChatScratchWords
                    content={item?.output || ''}
                    codeHighlight
                    chatRecordID={item?.ID}
                    predictRecordID={item?.predictRecordID}
                    getContainer={() => {
                        if (isFullScreen) {
                            return document.querySelector('.fullscreen-enabled');
                        }
                        return document.body;
                    }}
                    containerId={`containerId_${isFullScreen}`}
                >
                    <JsonOrMarkdownOrDiff content={item?.output || ''} onRefresh={refresh} />
                </ChatScratchWords>
            )}
            <SearchResults data={item?.searchResults} />
        </div>
    );
};

export const BotItem = (props: IChatRecordItemPorps) => {
    const [taskInfo] = useTaskInfo();
    const oneClickExpand = useChatOneClickExpand();
    const {item, index, data, openScore, refresh, getPopupContainer} = props;
    const expendRef = useRef<any>(null);
    useEffect(
        () => {
            expendRef.current?.onChange(!openScore || !oneClickExpand);
        },
        [openScore, oneClickExpand]
    );

    return (
        <div className={styles.botContainer}>
            <div className={styles.robot}>A{index + 1}</div>
            <div
                className={styles.botContent}
                style={{
                    width:
                        openScore && !!taskInfo?.isRoundScoring
                            ? 'calc(100% - 200px)'
                            : 'calc(100% - 24px)',
                    minWidth: '270px',
                    overflow: 'auto',
                    ...(item?.highlight === '0'
                        ? {backgroundColor: '#fffbd8'}
                        : {}),
                }}
            >
                <Expend
                    ref={expendRef}
                    background={
                        item?.highlight === '0'
                            ? 'linear-gradient(180deg, rgba(216, 228, 237, 0.00) 0%, #fffbd8 100%)'
                            : 'linear-gradient(180deg, rgba(216, 228, 237, 0.00) 0%, #e1ebf0 100%)'
                    }
                >
                    <div className={styles.content}>
                        <BotContentComp {...props} />
                    </div>
                </Expend>
            </div>
            {openScore && !!taskInfo?.isRoundScoring && (
                <Boundary>
                    <MsgItemScoreItem
                        modelID={data?.modelID}
                        chatID={item.ID}
                        item={item}
                        refresh={refresh}
                        getPopupContainer={getPopupContainer}
                    />
                </Boundary>
            )}
        </div>
    );
};
export const CustomerItem = ({
    item,
    index,
    refresh,
}: IChatRecordItemPorps) => {
    const [showOrigion, setShowOrigion] = useState(false);
    return (
        <div className={styles.customerContainer} id={`${item.ID}_highlight`}>
            <div className={styles.username}>Q{index + 1}</div>
            <FlexLayout
                style={{
                    borderRadius: '6px 0 6px 6px',
                    width: 'calc(100% - 24px)',
                    overflow: 'auto',
                    marginBottom: 5,
                }}
                align="stretch"
                gap={8}
            >
                <FlexLayout
                    justify="space-between"
                    style={{
                        padding: '4px 8px',
                        backgroundColor:
                            item?.highlight === '0' ? '#fffbd8' : '#e3f5ff',
                        flex: 2,
                    }}
                >
                    <div>
                        <MultiModalButton multiModal={item?.multiModal} />
                        {showOrigion ? (
                            <OriginCotent>{item?.input || ''}</OriginCotent>
                        ) : (
                            <Markdown
                                content={item?.input || ''}
                                codeHighlight
                            />
                        )}
                    </div>
                    <FlexLayout gap={4} align="center">
                        <MDChangeButton
                            showOrigion={showOrigion}
                            setShowOrigion={setShowOrigion}
                        />
                        <ChatRecordEditorButton
                            item={item}
                            type={ChatRecordEditorTypeEnum.Input}
                            onRefresh={refresh}
                        />
                        <Typography.Text
                            copyable={{
                                text: item?.input || '',
                                tooltips: '复制对话',
                                icon: (
                                    <CopyOutlined
                                        style={{
                                            color: '#000',
                                            width: '14px',
                                            height: '14px',
                                        }}
                                    />
                                ),
                            }}
                        />
                        <DeleteRecordButton
                            chatRecordItem={item}
                            onRefresh={refresh}
                            disabled={!!item?.score}
                        />
                    </FlexLayout>
                </FlexLayout>
            </FlexLayout>
        </div>
    );
};
