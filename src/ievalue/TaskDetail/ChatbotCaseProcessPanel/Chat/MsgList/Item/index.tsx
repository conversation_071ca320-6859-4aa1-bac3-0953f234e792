/* eslint-disable max-lines */
/* eslint-disable complexity */
import {CopyOutlined, FormOutlined} from '@ant-design/icons';
import {createIcon, message, Modal} from '@panda-design/components';
import {Tooltip, Typography} from 'antd';
import {useBoolean} from 'huse';
import {memo, useCallback, useEffect, useRef, useState} from 'react';
import {Boundary} from 'react-suspense-boundary';
import styled from '@emotion/styled';
import {default as RobotIcon} from '@/assets/ievalue/ChatRobot';
import RecordIcon from '@/assets/ievalue/Record';
import {Markdown} from '@/design/Markdown';
import {
    ChatOutputUpdateBody,
    ChatRecordItem,
    apiChatOutputUpdate,
} from '@/api/ievalue/case';
import {PromptExcuteResultListItem} from '@/api/ievalue/prompt-version';
import {TaskModelItem} from '@/api/ievalue/task';
import {EditorHerb} from '@/components/ievalue/EditorHerb';
import {Expend} from '@/components/ievalue/Expend';
import {FlexLayout} from '@/components/ievalue/FlexLayout';
import LoadingText from '@/components/ievalue/LoadingText';
import {ChatStatusEnum} from '@/constants/ievalue/case';
import {
    useChatState,
    useFindModel,
    useGroupPanekContext,
    useTaskInfo,
    useTaskStageID,
} from '@/hooks/ievalue/task';
import JsonOrMarkdownOrDiff from '@/components/ievalue/JsonOrMarkdownOrDiff';
import {MDChangeButton} from '@/components/ievalue/MDChangeButton';
import {ReasonContentButton} from '@/components/Evaluate/ModalCompare/ReasonContentButton';
import {ChatScratchWords} from '@/components/ievalue/ScratchWordsMarkdown';
import {SearchResults} from '@/components/ievalue/SearchResults';
import {debounceRefreshScratchWords} from '@/components/ievalue/ScratchWordsMarkdown/ScratchWordsProvider';
import {AgentRecordButton} from '../AgentRecordButton';
import {MsgItemScoreItem} from '../MsgItemScoreItem';
import {MultiModalButton} from '../MultiModalButton';
import styles from './index.module.less';
import OptionalRawButton from './OptionalRawButton';
import AudioLinkButton from './AudioLinkButton';
import MoreInfoButton from './MoreInfoButton';
export const IconRobot = createIcon(RobotIcon);
export const OriginCotent = styled.div`
    word-break: break-all;
    word-wrap: break-word;
    white-space: pre-wrap;
`;
interface IChatRecordItemPorps {
    item: ChatRecordItem;
    index?: number;
    data?: PromptExcuteResultListItem;
    onSave?: (item: ChatRecordItem) => void;
    openScore?: boolean;
    setOpenScore?: (open: boolean) => void;
    refresh?: (chatTaskID: string) => void;
    getPopupContainer?: () => HTMLElement;
    isFullScreen?: boolean;
}

const ToolComp = ({
    item,
    data,
    onOpen,
    onChangeScore,
    showOrigion,
    setShowOrigion,
}: IChatRecordItemPorps & {
    onOpen: (item: ChatRecordItem) => void;
    onChangeScore: () => void;
    showOrigion?: boolean;
    setShowOrigion?: (show: boolean) => void;
}) => {
    const [taskInfo] = useTaskInfo();
    const model = useFindModel(
        (model: TaskModelItem) => model.modelID === data?.modelID
    );
    return (
        <FlexLayout
            gap={8}
            justify="end"
            align="center"
            style={{width: '100%'}}
        >
            <MDChangeButton
                showOrigion={showOrigion}
                setShowOrigion={setShowOrigion}
            />
            {model?.modelType.includes('灵境') && (
                <AgentRecordButton
                    chatTaskID={data?.chatTaskID}
                    predictRecordID={item?.predictRecordID}
                    chatRecordID={item?.ID}
                />
            )}
            <Typography.Text
                copyable={{
                    text: item?.output || '',
                    tooltips: '复制对话',
                    icon: (
                        <CopyOutlined
                            style={{
                                color: '#000',
                                width: '14px',
                                height: '14px',
                                marginTop: '5px',
                            }}
                        />
                    ),
                }}
            />
            {taskInfo.stage !== 'TERMINATED' && (
                <>
                    <Tooltip title={'编辑结果'}>
                        <FormOutlined
                            style={{cursor: 'pointer'}}
                            onClick={() => onOpen(item)}
                        />
                    </Tooltip>
                    {!!taskInfo?.isRoundScoring && (
                        <Tooltip title="满意度评分">
                            <RecordIcon
                                onClick={onChangeScore}
                                style={{
                                    color: item.scoreUnChecked
                                        ? 'rgba(0, 0, 0, 0.88)'
                                        : '#2d70ff',
                                }}
                            />
                        </Tooltip>
                    )}
                </>
            )}
            <MoreInfoButton item={item} />
            <OptionalRawButton item={item} />
            {!!item?.audioLink && (
                <AudioLinkButton audioLink={item?.audioLink} />
            )}
        </FlexLayout>
    );
};

const BotContentComp = (props: IChatRecordItemPorps) => {
    const {setOpenScore, item, onSave, openScore, data, isFullScreen} = props;
    const [editorVal, setEditorVal] = useState<string>('');
    const [isModalVisible, {on: openModal, off: closeModal}] =
        useBoolean(false);
    const [showOrigion, setShowOrigion] = useState(false);
    const [taskInfo] = useTaskInfo();
    const stageID = useTaskStageID();
    const [chatID, setChatID] = useState<string>('');
    const onChangeScore = useCallback(
        () => {
            setOpenScore?.(!openScore);
        },
        [openScore, setOpenScore]
    );
    const onOpen = useCallback(
        (item: any) => {
            setChatID(item.ID);
            openModal();
            setEditorVal(item.output || '');
        },
        [openModal]
    );

    const handleOk = useCallback(
        async () => {
            const param: ChatOutputUpdateBody = {
                taskID: taskInfo.taskID,
                stageID,
                predictRecordID: item.predictRecordID,
                ID: Number(chatID),
                output: editorVal,
            };
            await apiChatOutputUpdate(param);
            onSave?.({...item, output: editorVal});
            closeModal();
            message.success('编辑成功');
        },
        [chatID, closeModal, editorVal, item, onSave, stageID, taskInfo.taskID]
    );
    useEffect(
        () => {
            debounceRefreshScratchWords();
        },
        [isFullScreen]
    );
    const {statusItem} = useChatState(data?.modelID || 0);
    if (
        statusItem?.status === ChatStatusEnum.ChatRunning
        && item?.output === ''
    ) {
        return <LoadingText text="正生成回答，请耐心等候..." />;
    }

    return (
        <>
            <div
                style={{overflow: 'hidden', padding: '0 8px', width: '100%'}}
            >
                <ToolComp
                    {...props}
                    onOpen={onOpen}
                    onChangeScore={onChangeScore}
                    showOrigion={showOrigion}
                    setShowOrigion={setShowOrigion}
                />
                <ReasonContentButton reasoningContent={item?.reasoningContent} />
                {showOrigion ? (
                    <OriginCotent>{item?.output || ''}</OriginCotent>
                ) : (
                    <ChatScratchWords
                        content={item?.output || ''}
                        codeHighlight
                        chatRecordID={item?.ID}
                        predictRecordID={item?.predictRecordID}
                        getContainer={() => {
                            if (isFullScreen) {
                                return document.querySelector('.fullscreen-enabled');
                            }
                            return document.body;
                        }}
                        containerId={`containerId_${isFullScreen}`}
                    >
                        <JsonOrMarkdownOrDiff content={item?.output || ''} />
                    </ChatScratchWords>
                )}
                <SearchResults data={item?.searchResults} />
            </div>
            <Modal
                title="编辑结果"
                width="1024px"
                open={isModalVisible}
                onCancel={closeModal}
                onOk={handleOk}
                maskClosable={false}
            >
                <EditorHerb
                    value={editorVal}
                    onChange={setEditorVal}
                    datasetID={taskInfo.datasetID}
                />
            </Modal>
        </>
    );
};

export const BotItem = memo((props: IChatRecordItemPorps) => {
    const [taskInfo] = useTaskInfo();
    const {open} = useGroupPanekContext();
    const {item, index, data, onSave, openScore, refresh, getPopupContainer} =
        props;
    const scoreRef = useRef<any>({});

    const onSaveScore = useCallback(
        (saveObj?: any) => {
            if (saveObj) {
                onSave?.(saveObj);
            }
            refresh?.(data?.chatTaskID);
        },
        [data?.chatTaskID, onSave, refresh]
    );
    const expendRef = useRef<any>(null);
    useEffect(
        () => {
            expendRef.current?.onChange(!openScore || !open);
        },
        [openScore, open]
    );

    return (
        <div className={styles.botContainer}>
            <div className={styles.robot}>A{index + 1}</div>
            <div
                className={styles.botContent}
                style={{
                    width:
                        openScore && !!taskInfo?.isRoundScoring
                            ? 'calc(100% - 200px)'
                            : 'calc(100% - 24px)',
                    minWidth: '270px',
                    overflow: 'auto',
                    ...(item?.highlight === '0'
                        ? {backgroundColor: '#fffbd8'}
                        : {}),
                }}
            >
                <Expend
                    ref={expendRef}
                    background={
                        item?.highlight === '0'
                            ? 'linear-gradient(180deg, rgba(216, 228, 237, 0.00) 0%, #fffbd8 100%)'
                            : 'linear-gradient(180deg, rgba(216, 228, 237, 0.00) 0%, #e1ebf0 100%)'
                    }
                >
                    <div className={styles.content}>
                        <BotContentComp {...props} />
                    </div>
                </Expend>
            </div>
            {openScore && !!taskInfo?.isRoundScoring && (
                <Boundary>
                    <MsgItemScoreItem
                        ref={scoreRef}
                        modelID={data?.modelID}
                        chatID={item.ID}
                        item={item}
                        onSave={onSaveScore}
                        getPopupContainer={getPopupContainer}
                    />
                </Boundary>
            )}
        </div>
    );
});
export const CustomerItem = memo(({item, index}: IChatRecordItemPorps) => {
    const [showOrigion, setShowOrigion] = useState(false);
    return (
        <div className={styles.customerContainer}>
            <div className={styles.username}>Q{index + 1}</div>
            <FlexLayout
                style={{
                    borderRadius: '6px 0 6px 6px',
                    width: 'calc(100% - 24px)',
                    overflow: 'auto',
                    marginBottom: 5,
                }}
                align="stretch"
                gap={8}
            >
                <FlexLayout
                    justify="space-between"
                    style={{
                        padding: '4px 8px',
                        backgroundColor:
                            item?.highlight === '0' ? '#fffbd8' : '#e3f5ff',
                        flex: 2,
                    }}
                >
                    <div>
                        <MultiModalButton multiModal={item?.multiModal} />
                        {showOrigion ? (
                            <OriginCotent>{item?.input || ''}</OriginCotent>
                        ) : (
                            <Markdown
                                content={item?.input || ''}
                                codeHighlight
                            />
                        )}
                    </div>
                    <FlexLayout
                        gap={8}
                        justify="end"
                        align="center"
                        style={{width: '100%'}}
                    >
                        <MDChangeButton
                            showOrigion={showOrigion}
                            setShowOrigion={setShowOrigion}
                        />
                        <Typography.Text
                            copyable={{
                                text: item?.input || '',
                                tooltips: '复制对话',
                                icon: (
                                    <CopyOutlined
                                        style={{
                                            color: '#000',
                                            width: '14px',
                                            height: '14px',
                                            marginTop: '5px',
                                        }}
                                    />
                                ),
                            }}
                        />
                    </FlexLayout>
                </FlexLayout>
                {/* {!!item?.image && (
                    <Image src={item?.image} height={200} />
                )} */}
            </FlexLayout>
        </div>
    );
});
