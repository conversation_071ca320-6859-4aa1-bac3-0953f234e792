import {useActionPending} from 'huse';
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from 'react';
import {FullScreenHandle} from 'react-full-screen';
import {apiChatClose, apiChatCreate} from '@/api/ievalue/case';
import {PromptExcuteResultListItem} from '@/api/ievalue/prompt-version';
import {FlexLayout} from '@/components/ievalue/FlexLayout';
import {ChatStatusEnum} from '@/constants/ievalue/case';
import useGetState from '@/hooks/ievalue/useGetState';
import {useChatState, useGroupCaseID} from '@/hooks/ievalue/task';
import {debounceRefreshScratchWords} from '@/components/ievalue/ScratchWordsMarkdown/ScratchWordsProvider';
import {ChatHederProps} from '..';
import {MsgList} from '../../MsgList';
import {ScoreForm} from '../../Score';
import {SendToolBar} from '../../SendToolBar';
import {GroupHederPanel} from '../GroupHederPanel';
import styles from '../index.module.less';

export interface FullChatMsgListProps extends ChatHederProps {
    data: PromptExcuteResultListItem;
    isEdit: boolean;
    onChatCreateSucceed?: (data: PromptExcuteResultListItem) => void;
    onChatCloseSucceed?: (data: PromptExcuteResultListItem) => void;
    onSendMsgSucceed?: (response: any) => void;
    handle: FullScreenHandle;
    setFull: (full: boolean) => void;
    full?: boolean;
    index?: number;
    onRefreshList?: (data: PromptExcuteResultListItem) => void;
    onCopy?: () => void;
    openScore: boolean;
    setOpenScore: (openScore: boolean) => void;
}
export const FullChatMsgList = memo(
    forwardRef((props: FullChatMsgListProps, ref: any) => {
        const {
            data,
            onSendMsgSucceed,
            onChatCreateSucceed,
            onChatCloseSucceed,
            full,
            openScore,
            setOpenScore,
        } = props;
        const {ID: predictRecordID, modelID} = data || {};
        const msgListRef = useRef<any>();
        const sendToolBarRef = useRef<any>({});
        const [toolbarHeight, setToolbarHeight] = useGetState(40);
        const [isFullEdit, setIsFullEdit] = useState(true);
        const {statusItem} = useChatState(modelID);
        const caseID = useGroupCaseID();
        const [actionChatClose, pendingCloseCount] = useActionPending(apiChatClose);
        const [actionChatCreate, pendingCreateCount] = useActionPending(apiChatCreate);
        const height = useMemo(
            () => {
                const toolbarH = sendToolBarRef.current ? toolbarHeight : 40;
                if (isFullEdit) {
                    return `calc(100% - ${toolbarH}px)`;
                } else if (statusItem?.status === ChatStatusEnum.ChatClosed) {
                    return `calc(100% - ${toolbarH}px - 250px)`;
                }
                return 'calc(100% - 40px)';
            },
            [isFullEdit, statusItem?.status, toolbarHeight]
        );
        const onChatCreate = useCallback(
            async () => {
                const closeResult = await actionChatClose({caseID, predictRecordID});
                onChatCloseSucceed?.(closeResult?.[0]);
                const createResult = await actionChatCreate({caseID, predictRecordID});
                onChatCreateSucceed?.(createResult?.[0]);
            },
            [
                actionChatClose,
                actionChatCreate,
                caseID,
                onChatCloseSucceed,
                onChatCreateSucceed,
                predictRecordID,
            ]
        );
        const refresh = useCallback(
            () => {
                if (full) {
                    msgListRef.current?.refresh();
                }
            },
            [full]
        );
        useImperativeHandle(
            ref,
            () => ({
                refresh,
                onChatCreate,
            }),
            [onChatCreate, refresh]
        );
        const getPopupContainer = useCallback(
            () => {
                return document.getElementById(`fullScreen_${modelID}`);
            },
            [modelID]
        );
        useEffect(
            () => {
                debounceRefreshScratchWords();
            },
            [full]
        );


        if (full) {
            return (
                <FlexLayout
                    direction="column"
                    className={styles.container}
                    gap={20}
                    id={`id_${predictRecordID}_${full}`}
                >
                    <GroupHederPanel
                        {...props}
                        setIsFullEdit={setIsFullEdit}
                        isFullEdit={isFullEdit}
                        onChatCreate={onChatCreate}
                        pendingCreateCount={pendingCreateCount || pendingCloseCount}
                    />
                    <FlexLayout className={styles.content} gap={12} id={`fullScreen_${modelID}`}>
                        <div className={styles.msgContainer}>
                            <MsgList
                                key={'full' + data.ID}
                                data={data}
                                height={height}
                                ref={msgListRef}
                                openScore={openScore}
                                setOpenScore={setOpenScore}
                                isFullScreen
                            />
                            <SendToolBar
                                onRefreshList={props.onRefreshList}
                                key={'fulltoolbar' + data.ID}
                                isEdit={isFullEdit}
                                setToolbarHeight={setToolbarHeight}
                                data={data}
                                ref={sendToolBarRef}
                                onSendMsgSucceed={onSendMsgSucceed}
                            />
                        </div>
                        <div className={styles.scoreContainer}>
                            {<ScoreForm modelID={modelID} getPopupContainer={getPopupContainer} />}
                        </div>
                    </FlexLayout>
                </FlexLayout>
            );
        }
        return (
            <GroupHederPanel
                {...props}
                setIsFullEdit={setIsFullEdit}
                isFullEdit={isFullEdit}
                onChatCreate={onChatCreate}
                pendingCreateCount={pendingCreateCount || pendingCloseCount}
            />
        );
    })
);
