import {Table} from 'antd';
import {head} from 'lodash';
import {ColumnProps, TablePaginationConfig} from 'antd/es/table';
import {CaseFeatureItem} from '@/api/ievalue/case';
import {
    CaseListStatusEnum,
    CaseListStatusMap,
} from '@/constants/ievalue/case';
import {TaskStatusEnum, TaskStatusMap} from '@/constants/ievalue/task';
import {ModelName} from '@/components/Evaluate/ModelName';
import {TablePopover} from '@/components/ievalue/TableMDPopover';
import {ImageColumnItem} from '../../GroupCaseTable';

interface GroupCaseTableProps {
    pending: boolean;
    dataSource: CaseFeatureItem[];
    operationCol: ColumnProps<CaseFeatureItem>;
    pagination: TablePaginationConfig;
    stageID: number;
}

const LLMFeatureTable = ({
    dataSource,
    operationCol,
    pending,
    pagination,
    stageID,
}: GroupCaseTableProps) => {
    const columns: Array<ColumnProps<CaseFeatureItem>> = [
        {
            title: '序号',
            dataIndex: 'line',
            key: 'line',
            fixed: 'left',
            align: 'center',
            width: 70,
        },
        {
            title: 'Query',
            dataIndex: 'query',
            key: 'query',
            ellipsis: {
                showTitle: false,
            },
            render: query => {
                return <TablePopover content={query} />;
            },
        },
        {
            title: '预期结果',
            dataIndex: 'referenceOutput',
            key: 'referenceOutput',
            ellipsis: {
                showTitle: false,
            },
            render: referenceOutput => {
                return <TablePopover content={referenceOutput} />;
            },
        },
        ImageColumnItem,
        ...(head(dataSource)?.output ?? []).map(
            (item: any, index: number) => {
                return {
                    title: () => {
                        return (
                            <ModelName
                                realName={item?.modelName}
                                pretendName={`结果${index + 1}`}
                                stageID={stageID}
                            />
                        );
                    },
                    dataIndex: `result+${index}`,
                    key: `result+${index}`,
                    ellipsis: {
                        showTitle: false,
                    },
                    render: (_: any, record: any) => {
                        return (
                            <TablePopover
                                content={record.output[index]?.modelOutput}
                            />
                        );
                    },
                };
            }
        ),
        {
            title: '执行状态',
            dataIndex: 'stageStatus',
            key: 'stageStatus',
            fixed: 'right',
            render: (stageStatus: TaskStatusEnum) => {
                return TaskStatusMap[stageStatus];
            },
        },
        {
            title: '用例状态',
            dataIndex: 'status',
            key: 'status',
            fixed: 'right',
            render: (status: CaseListStatusEnum) => {
                return <>{CaseListStatusMap[status]}</>;
            },
        },
        operationCol,
    ];

    return (
        <Table<CaseFeatureItem>
            rowKey="caseFeatureID"
            style={{width: '100%'}}
            columns={columns}
            dataSource={dataSource}
            loading={pending}
            pagination={pagination}
            size="small"
            scroll={{
                x: 'max-content', // 表格内容自适应
            }}
        />
    );
};

export default LLMFeatureTable;
