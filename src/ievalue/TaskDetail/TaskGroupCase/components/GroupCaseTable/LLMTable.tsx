import {Table} from 'antd';
import {ColumnProps} from 'antd/es/table';
import {useMemo} from 'react';
import {CaseItem} from '@/api/ievalue/case';
import {
    useGroupInfo,
    useTaskInfo,
    useTaskStrategyInfo,
} from '@/hooks/ievalue/task';
import {TableMDPopover} from '@/components/ievalue/TableMDPopover';
import {
    CaseListStatusEnum,
    CaseListStatusMap,
} from '@/constants/ievalue/case';
import {
    TaskEvaluateModeEnum,
    TaskStageEnum,
    TaskStatusEnum,
    TaskStatusMap,
} from '@/constants/ievalue/task';
import {getModelScoreColumn} from '@/components/Evaluate/TaskDetailUtils/util';
import {GroupCaseTableProps, ImageColumnItem} from '.';

const LLMTable = ({
    dataSource,
    operationCol,
    refresh,
    pending,
    pagination,
    groupStageInfo,
}: GroupCaseTableProps) => {
    const [taskInfo] = useTaskInfo();
    const strategyInfo = useTaskStrategyInfo();
    const [taskGroupInfo] = useGroupInfo();
    const columns: Array<ColumnProps<CaseItem>> = useMemo(
        () => {
            const includesPredicting = taskGroupInfo?.caseStageNum?.some(
                e => e.role === TaskStageEnum.PREDICTING
            );
            return [
                {
                    title: '序号',
                    dataIndex: 'line',
                    key: 'line',
                    fixed: 'left',
                    align: 'center',
                    width: 70,
                },
                {
                    title: 'Query',
                    dataIndex: 'query',
                    key: 'query',
                    ellipsis: {
                        showTitle: false,
                    },
                    render: (query: string, record: any) => {
                        return (
                            <TableMDPopover
                                content={query}
                                editKey="input"
                                datasetID={taskInfo.datasetID}
                                onRefresh={refresh}
                                titleName={'Query'}
                                caseID={record.caseID}
                                groupID={record.groupID}
                                stageID={record.stageID}
                            />
                        );
                    },
                },
                {
                    title: '预期结果',
                    dataIndex: 'referenceOutput',
                    key: 'referenceOutput',
                    ellipsis: {
                        showTitle: false,
                    },
                    render: (referenceOutput: string, record: any) => {
                        return (
                            <TableMDPopover
                                content={referenceOutput}
                                editKey="referenceOutput"
                                datasetID={taskInfo.datasetID}
                                onRefresh={refresh}
                                titleName={'预期结果'}
                                caseID={record.caseID}
                                groupID={record.groupID}
                                stageID={record.stageID}
                            />
                        );
                    },
                },
                ImageColumnItem,
                ...getModelScoreColumn(
                    dataSource,
                    groupStageInfo?.stageID,
                    taskInfo?.datasetID,
                    strategyInfo,
                    refresh,
                    includesPredicting,
                    taskInfo?.evaluateMode === TaskEvaluateModeEnum.AUTO
                ),
                {
                    title: '执行状态',
                    dataIndex: 'stageStatus',
                    key: 'stageStatus',
                    fixed: 'right',
                    render: (stageStatus: TaskStatusEnum) => {
                        return TaskStatusMap[stageStatus];
                    },
                },
                {
                    title: '用例状态',
                    dataIndex: 'status',
                    key: 'status',
                    fixed: 'right',
                    render: (status: CaseListStatusEnum) => {
                        return <>{CaseListStatusMap[status]}</>;
                    },
                },
                operationCol,
            ];
        },
        [
            dataSource,
            groupStageInfo?.stageID,
            operationCol,
            refresh,
            strategyInfo,
            taskGroupInfo?.caseStageNum,
            taskInfo?.datasetID,
            taskInfo?.evaluateMode,
        ]
    );

    return (
        <Table<CaseItem>
            rowKey="caseID"
            columns={columns}
            dataSource={dataSource}
            loading={pending}
            pagination={pagination}
            size="small"
            scroll={{
                x: 'max-content', // 表格内容自适应
            }}
        />
    );
};

export default LLMTable;
