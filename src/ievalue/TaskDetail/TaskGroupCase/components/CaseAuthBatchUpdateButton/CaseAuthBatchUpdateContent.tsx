import {Form, FormInstance, Switch} from 'antd';
import {message} from '@panda-design/components';
import {useCallback} from 'react';
import {FormOnFinish} from '@/utils/ievalue/typing';
import {UserSelect} from '@/components/ievalue/UserSelect';
import {useTaskTaskID} from '@/hooks/ievalue/task';
import {apiTaskCaseAuthBatchUpdate} from '@/api/ievalue/case';
import {CaseAuthBatchUpdateProps} from '.';

interface CaseAuthBatchUpdateContentProps extends CaseAuthBatchUpdateProps {
    form: FormInstance;
    onClose: () => void;
}

const CaseAuthBatchUpdateContent = ({
    onFinish,
    onClose,
    form,
    caseIDs,
}: CaseAuthBatchUpdateContentProps) => {
    const taskID = useTaskTaskID();
    const handleFinish: FormOnFinish<any> = useCallback(
        async formData => {
            try {
                await apiTaskCaseAuthBatchUpdate({
                    taskID,
                    ...formData,
                    caseIDs,
                });
                message.success('修改负责人成功');
                onFinish();
                onClose();
            } catch (e) {
                /* empty */
            }
        },
        [caseIDs, onClose, onFinish, taskID]
    );

    return (
        <Form
            form={form}
            onFinish={handleFinish}
            labelCol={{flex: '140px'}}
            labelAlign="left"
            initialValues={{allowUserRepeat: true}}
        >
            <Form.Item
                name="allowUserRepeat"
                label="允许人员重复分配"
                valuePropName="checked"
            >
                <Switch />
            </Form.Item>
            <Form.Item
                label="负责人"
                name="username"
                rules={[{required: true, message: '请选择负责人'}]}
            >
                <UserSelect labelInValue={false} />
            </Form.Item>
        </Form>
    );
};

export default CaseAuthBatchUpdateContent;
