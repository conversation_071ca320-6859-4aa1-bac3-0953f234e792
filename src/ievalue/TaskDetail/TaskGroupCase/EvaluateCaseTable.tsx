/* eslint-disable max-lines */
/* eslint-disable complexity */
import {css} from '@emotion/css';
import styled from '@emotion/styled';
import {Flex, Form, Input, Popover, Select, Table} from 'antd';
import type {ColumnsType} from 'antd/es/table';
import {useRequestCallback} from 'huse';
import {debounce, head, isEmpty, isNil, uniq} from 'lodash';
import {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {Markdown} from '@/design/Markdown';
import {
    apiCaseEvaluateUpsert,
    apiEvaluateCaseList,
    apiEvaluateRecordUpdate,
    ChoiceItem,
    EvaluateCaseItem,
    EvaluateCaseListResponse,
    ScoreItem,
} from '@/api/ievalue/case';
import {CustomBoundary} from '@/components/ievalue/Boundary';
import {ColumnSelect} from '@/components/ievalue/ColumnSelect';
import {FlexLayout} from '@/components/ievalue/FlexLayout';
import {
    TableMDPopover,
    TablePopover,
} from '@/components/ievalue/TableMDPopover';
import {EditNodeProvider} from '@/components/ievalue/TableMDPopover/TableMDProvider';
import {
    CaseListStatusEnum,
    CaseListStatusMap,
} from '@/constants/ievalue/case';
import {Evaluate_Split_Str} from '@/constants/ievalue/evaluate';
import {
    TargetEnum,
    TaskStageEnum,
    TaskStatusEnum,
    TaskStatusMap,
} from '@/constants/ievalue/task';
import {
    useGroupStageInfo,
    useTaskInfo,
    useTaskTaskID,
} from '@/hooks/ievalue/task';
import {useStrategyTaskList} from '@/hooks/ievalue/task';
import {getMultiModalContent} from '@/components/Evaluate/TaskDetailUtils/util';
import {MultiModalContent} from '@/components/TaskGroup/MultiModalContent';
import AddTagPanel from '../TaskGroupDetail/components/TagPanelComponent/AddTagPanel';
import {CreateCaseButton} from './components/CreateCaseButton';
import {DeleteCaseButton} from './components/DeleteCaseButton';
import {CustomFilterButton} from './components/GroupTransferPanel/Components/CustomFilterButton';
import {ViewCaseLog} from './components/ViewCaseLog';
import ShareGroup from './ShareGroup';
import {AgentRecordButton} from './components/AgentRecordButton';

const {Search} = Input;

const Container = styled.div`
    .ant-5-table-wrapper
        .ant-5-table.ant-5-table-small
        .ant-5-table-tbody
        > tr
        > td {
        padding: 0 !important;
        vertical-align: middle !important;
    }
    .ant-5-table-body {
        table {
            height: 100% !important;
        }
    }
    table {
        border-bottom: 1px solid rgb(217, 217, 217);
        border-right: 1px solid rgb(217, 217, 217);
        th,
        td {
            border-collapse: collapse;
            border: 1px solid rgb(217, 217, 217);
        }
        thead {
            tr:nth-child(2) {
                th {
                    border-bottom: 0;
                }
            }
        }
        .ant-5-table-thead {
            position: sticky;
            top: 0px;
            z-index: !;
        }
    }
`;

interface Props {
    groupID: number;
    taskstage: any;
    disabled?: boolean;
}

const datasetColumn = ['多模态数据', 'L0', 'L1', '参考文章', '参考答案', 'messages'];

const datasetMap: any = {
    多模态数据: 'multiModal',
    L0: 'l0',
    L1: 'l1',
    参考答案: 'referenceOutput',
    参考文章: 'reference',
    messages: 'messages',
};

export default function CaseTable({groupID, taskstage, disabled}: Props) {
    const columeUpdateRef = useRef(true);
    const taskID = useTaskTaskID();
    const [strategyList] = useStrategyTaskList();
    const metrics = useMemo(
        () => head(strategyList)?.metric ?? [],
        [strategyList]
    );
    const [taskInfo] = useTaskInfo();
    const [groupStageInfo] = useGroupStageInfo();
    const getEvaluateUpsertParams = useCallback(
        (
            record: Record<string, any>,
            model: Record<string, any>,
            attr: string,
            value: any
        ) => {
            const params = {
                predictRecordID: model.predictRecordID,
                stageID: record.stageID,
                groupID: record.groupID,
                caseID: record.caseID,
                [attr]: value,
                autoPass: true,
                taskID,
            };
            return params;
        },
        [taskID]
    );
    const getEvaluateUpdateParams = useCallback(
        (
            record: Record<string, any>,
            model: Record<string, any>,
            attr: string,
            value: any
        ) => {
            const params = {
                ID: model.evaluateCaseRecordID,
                predictRecordID: model.predictRecordID,
                evaluateCaseID: model.evaluateCaseID,
                [attr]: value,
                taskID,
            };
            return params;
        },
        [taskID]
    );
    const resetChoiceFilters = useCallback(
        (choices: any[]) => {
            const choiceFilters: any[] = [];
            if (choices?.length) {
                for (const choice of choices) {
                    choiceFilters.push({
                        text: choice.name,
                        value: choice.score,
                    });
                }
            }
            return choiceFilters;
        },
        []
    );
    const [searchData, setSearchData] = useState({});
    const [searchModelFilters, setSearchModelFilters] = useState({});
    const [optionalMatch, setOptionalMatch] = useState({});
    const [pn, setPn] = useState(1);
    const [size, setSize] = useState(20);
    const [load, result] = useRequestCallback(apiEvaluateCaseList, {
        groupID,
        pn,
        size,
        target: taskInfo.target,
        ...searchData,
        modelFilters: searchModelFilters,
        optionalMatch,
    });
    const [modelTitles, setModelTitles] = useState<string[]>([]);
    const [headRecords, setHeadRecords] = useState<any>({});
    const [optionalRaw, setOptionalRaw] = useState<string[]>([]);
    const [data, setData] = useState<EvaluateCaseListResponse>();
    const [loading, setLoading] = useState<boolean>(false);
    const handleCustomFilterFinish = useCallback(
        (column: string, filterList: string[]) => {
            setOptionalMatch(pre => {
                return {...pre, [column]: filterList};
            });
            setPn(1);
        },
        []
    );

    const changeNote = useCallback(
        async (
            {target}: any,
            record: Record<string, any>,
            model: Record<string, any>
        ) => {
            try {
                const note = target?.value;
                const upsertparams = {
                    ...getEvaluateUpsertParams(record, model, 'note', note),
                };
                const updateParams = {
                    ...getEvaluateUpdateParams(record, model, 'note', note),
                };
                if (taskstage === 'ACCEPTING') {
                    await apiEvaluateRecordUpdate(updateParams);
                } else {
                    await apiCaseEvaluateUpsert(upsertparams);
                }
            } catch (error) {
                // eslint-disable-next-line no-console
            } finally {
                load();
            }
        },
        [getEvaluateUpdateParams, getEvaluateUpsertParams, load, taskstage]
    );
    const changeScore = useCallback(
        async (
            {target}: any,
            record: Record<string, any>,
            model: Record<string, any>,
            metric: any
        ) => {
            try {
                const updateScore = target?.value;
                let selectedChoice = {
                    name: updateScore,
                };
                if (metric.choices) {
                    selectedChoice = metric.choices.find(
                        (choice: ChoiceItem) => {
                            return updateScore === choice.score;
                        }
                    );
                }
                const score: ScoreItem[] = [
                    {
                        desc: metric.desc,
                        metric: metric.metric,
                        score: Number(updateScore),
                        scoreName: selectedChoice.name,
                        evaluateMode: 'MANUAL',
                    },
                ];
                // Object.values(model.scoreMap)
                const upsertparams = {
                    ...getEvaluateUpsertParams(record, model, 'score', score),
                };
                const updateParams = {
                    ...getEvaluateUpdateParams(record, model, 'score', score),
                };
                if (taskstage === 'ACCEPTING') {
                    await apiEvaluateRecordUpdate(updateParams);
                } else {
                    await apiCaseEvaluateUpsert(upsertparams);
                }
            } catch (error) {
                // eslint-disable-next-line no-console
            } finally {
                load();
            }
        },
        [getEvaluateUpdateParams, getEvaluateUpsertParams, load, taskstage]
    );

    const onFilter = (filters: any) => {
        const filterObj = Object.entries(filters).reduce(
            (resultObj: any, [key, value]) => {
                if (!isNil(value)) {
                    const [modelName, metric, customName] =
                        key.split(Evaluate_Split_Str);
                    resultObj[modelName] = resultObj[modelName] || {
                        scoreFilters: {},
                    };
                    const scoreFilters = resultObj[modelName].scoreFilters;
                    scoreFilters[metric] = scoreFilters.hasOwnProperty(metric)
                        ? {
                            ...scoreFilters[metric],
                            ...(customName
                                ? {[customName]: value}
                                : {score: value}),
                        }
                        : customName
                            ? {[customName]: value}
                            : {score: value};
                }
                return resultObj;
            },
            {}
        );
        setSearchModelFilters(filterObj);
    };

    // const getColumnSearchProps = (title: string): ColumnType<any> => ({
    //     filterDropdown: ({setSelectedKeys, selectedKeys, confirm, clearFilters}) => (
    //         <div style={{padding: 8}} onKeyDown={e => e.stopPropagation()}>
    //             <Input
    //                 placeholder={title}
    //                 value={selectedKeys[0]}
    //                 onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
    //                 style={{marginBottom: 8, display: 'block'}}
    //             />
    //             <Space>
    //                 <Button onClick={clearFilters} size="small" style={{width: 90}}>
    //                     重置
    //                 </Button>
    //                 <Button
    //                     type="primary"
    //                     onClick={() => confirm({closeDropdown: true})}
    //                     icon={<SearchOutlined />}
    //                     size="small"
    //                     style={{width: 90}}
    //                 >
    //                     确定
    //                 </Button>
    //             </Space>
    //         </div>
    //     ),
    //     filterIcon: (filtered: boolean) => (
    //         <SearchOutlined style={{color: filtered ? '#1677ff' : undefined}} />
    //     ),
    // });
    const isPromptAndFlowTask = useMemo(
        () => {
            return [TargetEnum.PromptFlow, TargetEnum.Prompt].includes(
                taskInfo?.target
            );
        },
        [taskInfo?.target]
    );
    const getTitleChildren = useCallback(
        (title: string) => {
            const children: any[] = [{
                title: '序号',
                dataIndex: 'line',
                key: 'line',
                fixed: 'left',
                align: 'center',
                width: 70,
            }];
            if (isPromptAndFlowTask) {
                children.push({
                    title: '预期结果',
                    dataIndex: title,
                    key: title,
                    width: 200,
                    render: (_: any, record: any) => {
                        return (
                            <TableMDPopover
                                content={
                                    record?.records?.[title]
                                        ?.promptReferenceOutput || '--'
                                }
                                editKey="promptReferenceOutput"
                                datasetID={taskInfo.datasetID}
                                onRefresh={load}
                                titleName={'预期结果'}
                                caseID={record.caseID}
                                groupID={record.groupID}
                                stageID={record.stageID}
                            />
                        );
                    },
                });
            }
            children.push({
                title: '结果',
                dataIndex: 'output',
                key: `${title}-output`,
                width: 200,
                render: (_: any, record: any) => {
                    let defaultValue: string = '';
                    if (record?.records) {
                        defaultValue = record?.records[title].output;
                    }
                    return (
                        <TableMDPopover
                            content={defaultValue}
                            datasetID={taskInfo.datasetID}
                            onRefresh={load}
                            titleName={`【${title}】结果`}
                            caseID={record.caseID}
                            groupID={record.groupID}
                            stageID={record.stageID}
                            recordID={record?.records[title].predictRecordID}
                        />
                    );
                },
            });
            children.push({
                title: '推理耗时（秒）',
                dataIndex: 'timeUsed',
                key: `${title}-timeUsed`,
                width: 110,
                render: (_: any, record: any) => {
                    const output = record?.records[title];
                    const timeUsed = output?.timeUsed ?? '--';
                    const status = output?.status;
                    return <Flex justify="center">{status === 'SUCCESS' ? timeUsed : '--'}</Flex>;
                },
            });
            for (const metric of metrics) {
                const choiceFilters: any[] = resetChoiceFilters(metric.choices);
                children.push({
                    title: metric.desc,
                    dataIndex: metric.metric,
                    key: `${title}${Evaluate_Split_Str}${metric.metric}`,
                    filters: choiceFilters.length > 0 ? choiceFilters : null,
                    width: 120,
                    render: (_: any, record: any) => {
                        let defaultValue: any = '';
                        let evaluateMode: string = '';
                        if (
                            record?.records
                            && !isEmpty(record?.records[title].scoreMap)
                        ) {
                            const scoreName: string =
                                record.records[title].scoreMap[metric.metric]
                                    ?.scoreName === 'null'
                                    ? '评分失败'
                                    : record.records[title].scoreMap[
                                        metric.metric
                                    ]?.scoreName;
                            defaultValue = scoreName;
                            evaluateMode =
                                record.records[title].scoreMap[metric.metric]
                                    ?.evaluateMode;
                        }
                        const operationDisabled = ![
                            'EVALUATING',
                            'ACCEPTING',
                            'AUDITING_FORWARD',
                            'AUDITING',
                        ].includes(taskstage);
                        const ScoreItem = metric?.choices ? (
                            <Select
                                size="small"
                                defaultValue={
                                    defaultValue === ''
                                        ? undefined
                                        : defaultValue
                                }
                                style={{width: 100}}
                                fieldNames={{label: 'name', value: 'score'}}
                                options={metric?.choices}
                                placeholder="请选择"
                                onChange={value => {
                                    changeScore(
                                        {target: {name: 'test', value}},
                                        record,
                                        record?.records[title],
                                        metric
                                    );
                                }}
                                disabled={operationDisabled || disabled}
                            />
                        ) : (
                            <Input
                                defaultValue={defaultValue}
                                placeholder="请输入"
                                onBlur={value => {
                                    changeScore(
                                        value,
                                        record,
                                        record?.records[title],
                                        metric
                                    );
                                }}
                                disabled={operationDisabled || disabled}
                            />
                        );
                        return (
                            <FlexLayout
                                align="center"
                                style={{
                                    width: '100%',
                                    height: '100%',
                                    maxWidth: 250,
                                    maxHeight: 300,
                                }}
                            >
                                {ScoreItem}
                                {evaluateMode?.length > 0 && (
                                    <span className={`badge ${evaluateMode}`}>
                                        <span className="check-mark">
                                            {evaluateMode === 'AUTO'
                                                ? '自动'
                                                : '手工'}
                                        </span>
                                    </span>
                                )}
                            </FlexLayout>
                        );
                    },
                });
                if (!isNil(metric?.output)) {
                    children.push({
                        title: `${metric.desc}打分原因`,
                        dataIndex: metric.metric,
                        key: `${title}${Evaluate_Split_Str}${metric.metric}${Evaluate_Split_Str}output`,
                        width: 230,
                        render: (_: any, record: any) => {
                            const {output} =
                                record?.records?.[title]?.scoreMap[
                                    metric?.metric
                                ] ?? {};
                            return (
                                <TableMDPopover
                                    content={output}
                                    datasetID={taskInfo.datasetID}
                                    caseID={record.caseID}
                                    groupID={record.groupID}
                                    stageID={record.stageID}
                                />
                            );
                        },
                    });
                }
                if (metric?.confidence) {
                    children.push({
                        title: `${metric.desc}置信度`,
                        dataIndex: metric.metric,
                        key: `${title}${Evaluate_Split_Str}${metric.metric}${Evaluate_Split_Str}confidence`,
                        width: 140,
                        // ...getColumnSearchProps(`${metric.desc}置信度`),
                        filters:
                            metric?.confidence?.map(item => ({
                                text: item,
                                value: item,
                            })) ?? [],
                        render: (_: any, record: any) => {
                            const {confidence} =
                                record?.records?.[title]?.scoreMap[
                                    metric?.metric
                                ] ?? {};
                            // eslint-disable-next-line no-negated-condition
                            return !isNil(confidence) ? confidence : '-';
                        },
                    });
                }
            }
            children.push({
                title: '备注',
                dataIndex: 'note',
                key: `${title}-note`,
                width: 200,
                render: (_: any, record: any) => {
                    let defaultValue: string = '';
                    if (record?.records) {
                        defaultValue = record?.records[title].note;
                    }
                    return (
                        <Popover
                            placement="rightTop"
                            content={
                                defaultValue.length > 0 && (
                                    <div
                                        style={{
                                            maxWidth: 300,
                                            maxHeight: 250,
                                            overflowY: 'auto',
                                        }}
                                    >
                                        <Markdown
                                            content={defaultValue}
                                            codeHighlight
                                        />
                                    </div>
                                )
                            }
                        >
                            <Input.TextArea
                                style={{height: '100%', resize: 'none'}}
                                name="note"
                                defaultValue={defaultValue}
                                draggable={false}
                                bordered={false}
                                placeholder="请输入备注"
                                onChange={debounce(
                                    e =>
                                        changeNote(
                                            e,
                                            record,
                                            record?.records[title]
                                        ),
                                    1000
                                )}
                                disabled={disabled}
                            />
                        </Popover>
                    );
                },
            });
            children.push({
                title: '标签',
                dataIndex: 'tags',
                key: `${title}-tags`,
                width: 250,
                render: (_: any, record: any) => {
                    return (
                        <div
                            style={{
                                width: '100%',
                                height: '100%',
                            }}
                        >
                            <AddTagPanel
                                caseID={record.caseID}
                                stageID={record.stageID}
                                refresh={load}
                                recordItem={record?.records?.[title]}
                            />
                        </div>
                    );
                },
            });
            children.push({
                title: '航线',
                dataIndex: 'agent',
                key: `${title}-agent`,
                width: 100,
                render: (_: any, record: any) => {
                    const {hasLinkRecord, predictRecordID} =
                        record?.records?.[title];
                    return hasLinkRecord && !!predictRecordID ? (
                        <FlexLayout justify="center" style={{width: '100%'}}>
                            <AgentRecordButton
                                taskID={taskID}
                                predictRecordID={predictRecordID}
                            />
                        </FlexLayout>
                    ) : (
                        <TablePopover content="--" />
                    );
                },
            });
            return children;
        },
        [
            changeNote,
            changeScore,
            disabled,
            isPromptAndFlowTask,
            load,
            metrics,
            resetChoiceFilters,
            taskID,
            taskInfo.datasetID,
            taskstage,
        ]
    );

    useEffect(
        () => {
            if (
                data?.caseList?.length
                && modelTitles?.length === 0
                && columeUpdateRef.current
            ) {
                columeUpdateRef.current = false;
                const headData = head(data?.caseList) ?? ({} as EvaluateCaseItem);
                const optionRawList = Object.keys(
                    JSON.parse(headData?.optionalRaw || '{}')
                );
                const headerList = uniq(headData?.header?.split(',') ?? []).filter(
                    item => optionRawList.includes(item)
                );
                const optionalRawList = optionRawList.filter(
                    item => !headerList.includes(item)
                );
                setModelTitles(Object.keys(headData?.records ?? {}));
                setHeadRecords(headData?.records ?? {});
                setOptionalRaw([...headerList, ...optionalRawList]);
            }
        },
        [data?.caseList, modelTitles?.length]
    );
    const tableHeight: number = window.innerHeight - 400;
    useEffect(
        () => {
            if (result.data) {
                setLoading(false);
                setData(result.data);
            }
        },
        [result]
    );

    useEffect(
        () => {
            setLoading(true);
            load();
        },
        [load]
    );

    const [selectList, setSelectList] = useState<any[]>([]);
    const allSelectOptionList = useMemo(
        () => {
            const selectList: string[] = [
                isPromptAndFlowTask ? '变量' : 'Query',
                '预期结果',
            ];
            const selectOptions: string[] = selectList.concat(datasetColumn);
            const headData = (head(data?.caseList) ?? {}) as EvaluateCaseItem;
            datasetColumn?.forEach(item => {
                if (headData?.[datasetMap[item] as keyof EvaluateCaseItem]) {
                    selectList.push(item);
                }
            });
            for (const i of optionalRaw) {
                selectOptions.push(i);
                selectList.push(i);
            }
            selectOptions.push('执行状态');
            selectOptions.push('用例状态');
            selectList.push('执行状态');
            selectList.push('用例状态');
            setSelectList(selectList);
            return selectOptions;
        },
        [data?.caseList, isPromptAndFlowTask, optionalRaw]
    );

    const columns: ColumnsType<EvaluateCaseItem> = [{
        title: '序号',
        dataIndex: 'line',
        key: 'line',
        fixed: 'left',
        align: 'center',
        width: 70,
    }];
    if (selectList.includes('Query')) {
        columns.push({
            title: 'Query',
            dataIndex: 'input',
            key: 'input',
            width: 200,
            fixed: 'left',
            render: (input, record) => {
                return (
                    <TableMDPopover
                        content={input}
                        editKey="input"
                        datasetID={taskInfo.datasetID}
                        onRefresh={load}
                        titleName={'Query'}
                        caseID={record.caseID}
                        groupID={record.groupID}
                        stageID={record.stageID}
                    />
                );
            },
        });
    }
    if (selectList.includes('变量')) {
        columns.push({
            title: '变量',
            dataIndex: 'input',
            key: 'input',
            width: 200,
            fixed: 'left',
            render: (input, record) => {
                const contentHtml = record.variables?.map((item: any) => {
                    return (
                        <div key={item.key}>
                            {item.key}：{item.value}
                        </div>
                    );
                });
                return <TablePopover content={contentHtml} />;
            },
        });
    }
    if (selectList.includes('预期结果') && !isPromptAndFlowTask) {
        columns.push({
            title: '预期结果',
            dataIndex: 'referenceOutput',
            key: 'referenceOutput',
            width: 200,
            render: (referenceOutput, record) => {
                return (
                    <TableMDPopover
                        content={referenceOutput}
                        editKey="referenceOutput"
                        datasetID={taskInfo.datasetID}
                        onRefresh={load}
                        titleName={'预期结果'}
                        caseID={record.caseID}
                        groupID={record.groupID}
                        stageID={record.stageID}
                    />
                );
            },
        });
    }
    for (const title of datasetColumn || []) {
        if (selectList.includes(title)) {
            columns.push({
                title,
                dataIndex: title,
                key: title,
                width: 200,
                render: (_: any, record: any) => {
                    const content = record[datasetMap[title]] ?? '';
                    if (!content) {
                        return <></>;
                    }
                    return (
                        <TablePopover
                            popContent={
                                title === '多模态数据'
                                    ? <MultiModalContent multiModal={content} />
                                    : undefined
                            }
                            content={getMultiModalContent(content)}
                        />
                    );
                },
            });
        }
    }
    for (const title of optionalRaw || []) {
        if (selectList.includes(title)) {
            columns.push({
                title: (
                    <FlexLayout justify="space-between" align="center">
                        <div>{title}</div>
                        <CustomFilterButton
                            column={title}
                            onFinish={handleCustomFilterFinish}
                        />
                    </FlexLayout>
                ),
                dataIndex: 'optionalRaw',
                key: title,
                width: 200,
                render: (_: any, record: any) => {
                    const value = JSON.parse(record.optionalRaw)[title] ?? '';
                    const resultValue =
                        typeof value === 'object'
                            ? JSON.stringify(value)
                            : `${value}`;
                    return <TablePopover content={resultValue} />;
                },
            });
        }
    }
    for (const title of modelTitles || []) {
        const {promptID, promptVersionID} = headRecords[title];
        columns.push({
            title:
                promptID && promptVersionID
                    ? title?.replace(`{{${promptID}-${promptVersionID}}}`, '')
                    : title,
            children: getTitleChildren(title),
            dataIndex: 'result',
            key: title,
            fixed: 'left',
        });
    }
    if (selectList.includes('执行状态')) {
        columns.push({
            title: '执行状态',
            dataIndex: 'stageStatus',
            key: 'stageStatus',
            width: 100,
            render: (stageStatus: TaskStatusEnum) => {
                return <TablePopover content={TaskStatusMap[stageStatus]} />;
            },
        });
    }
    if (selectList.includes('用例状态')) {
        columns.push({
            title: '用例状态',
            dataIndex: 'status',
            key: 'status',
            width: 100,
            render: (status: CaseListStatusEnum) => {
                return <TablePopover content={CaseListStatusMap[status]} />;
            },
        });
    }
    columns.push({
        title: '操作',
        key: 'operation',
        dataIndex: 'operation',
        fixed: 'right',
        width: 80,
        render: (_: any, record: any) => {
            const {caseID} = record;
            return (
                <>
                    {taskstage === 'PREDICTING' && (
                        <ViewCaseLog caseID={caseID} record={record} />
                    )}
                    {groupStageInfo?.role === TaskStageEnum.EVALUATING
                        && !disabled && (
                        <DeleteCaseButton
                            caseID={caseID}
                            onRefresh={load}
                        />
                    )}
                </>
            );
        },
    });
    columns.push({
        title: (
            <ColumnSelect
                options={allSelectOptionList}
                onChange={setSelectList}
                selectList={selectList}
            />
        ),
        key: 'filterSelect',
        fixed: 'right',
        width: 40,
    });
    const [form] = Form.useForm<FormData>();
    const prefixSelector = (
        <Form.Item name="prefix" noStyle>
            <Select style={{width: '100px'}}>
                {!isPromptAndFlowTask && (
                    <Select.Option value="input">Query</Select.Option>
                )}
                {isPromptAndFlowTask && (
                    <Select.Option value="variables">变量</Select.Option>
                )}
                {optionalRaw.map(item => (
                    <Select.Option value={`optionalRaw-${item}`} key={item}>
                        {item}
                    </Select.Option>
                ))}
            </Select>
        </Form.Item>
    );
    const onSearch = () => {
        form.submit();
    };
    const onSubmit = useCallback(
        (formData: any) => {
            const searchData: any = {};
            if (formData.prefix === 'input') {
                searchData.input = formData?.keyword;
            } else if (formData.prefix === 'variables') {
                searchData.variables = formData?.keyword;
            } else {
                const optionalRawTitle: string =
                    formData.prefix.split('optionalRaw-')[1];
                searchData.optionalRaw = {
                    [optionalRawTitle]: formData?.keyword,
                };
            }
            searchData.stageStatus = formData?.stageStatus;
            setSearchData(searchData);
            setPn(1);
            setLoading(true);
        },
        []
    );
    return (
        <Container>
            <FlexLayout justify="space-between" align="baseline">
                <CustomBoundary.InlineLoading>
                    <CreateCaseButton onRefresh={load} />
                </CustomBoundary.InlineLoading>
                <Form
                    form={form}
                    name="horizontal_login"
                    layout="inline"
                    style={{float: 'right', margin: '-10px 0 10px 0'}}
                    initialValues={{
                        prefix: isPromptAndFlowTask ? 'variables' : 'input',
                    }}
                    onFinish={onSubmit}
                >
                    <Form.Item name="stageStatus" label="执行状态">
                        <Select
                            allowClear
                            style={{width: 150}}
                            options={[
                                {value: 'WAITING', label: '等待'},
                                {value: 'RUNNING', label: '执行中'},
                                {value: 'SUCCESS', label: '成功'},
                                {value: 'FAIL', label: '失败'},
                                {value: 'CANCEL', label: '取消'},
                                {value: 'FINISH', label: '已完成'},
                            ]}
                            placeholder="请选择执行状态"
                            onChange={onSearch}
                        />
                    </Form.Item>
                    <Form.Item name="keyword">
                        <Search
                            addonBefore={prefixSelector}
                            placeholder="请输入关键字"
                            onSearch={onSearch}
                        />
                    </Form.Item>
                    <Form.Item>
                        <ShareGroup groupID={groupID} taskID={taskID} />
                    </Form.Item>
                </Form>
            </FlexLayout>
            <EditNodeProvider>
                <Table<EvaluateCaseItem>
                    bordered
                    rowKey="caseID"
                    columns={columns}
                    dataSource={data?.caseList}
                    loading={loading}
                    onChange={(_, filters) => {
                        onFilter(filters);
                    }}
                    pagination={{
                        current: pn,
                        pageSize: size,
                        showSizeChanger: true,
                        total: data?.total ?? 0,
                        onChange: (page: number, pageSize: number) => {
                            setSize(pageSize);
                            setPn(page);
                        },
                    }}
                    size="small"
                    scroll={{x: 'max-content', y: tableHeight}}
                    className={css`
                        td {
                            overflow: hidden;
                        }
                    `}
                />
            </EditNodeProvider>
        </Container>
    );
}
