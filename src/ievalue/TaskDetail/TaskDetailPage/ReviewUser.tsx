import {Space, Typography} from 'antd';
import {UserAvatar} from '@/components/ievalue/UserAvatar/UserAvatar';

interface ReviewUserProps {
    userName: string;
    title?: string;
}
export const ReviewUser = ({userName, title}: ReviewUserProps) => {
    return (
        <Space>
            {userName ? (
                <UserAvatar user={userName} size={24} />
            ) : (
                <Typography.Text>未分配</Typography.Text>
            )}
            <Typography.Text>{title ? title : userName}</Typography.Text>
        </Space>
    );
};
