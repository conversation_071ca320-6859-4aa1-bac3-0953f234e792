import {Flex, Form, FormInstance, Select, Switch} from 'antd';
import {TeamOutlined} from '@ant-design/icons';
import {Button, message} from '@panda-design/components';
import {useCallback, useMemo} from 'react';
import {head, uniq} from 'lodash';
import {useRequest} from 'huse';
import {useTaskCaseStats, useTaskTaskID} from '@/hooks/ievalue/task';
import {GroupUserSelect}
    from '@/ievalue/TaskPage/TaskCreate/CreateTask/Components/EvaluateConfig/GroupConfigFormItem/GroupUserSelect';
import {UserSelect} from '@/components/ievalue/UserSelect';
import {apiTaskCaseDispatch, apiTaskCasePureList} from '@/api/ievalue/task';
import {TaskAssignButtonProps} from '.';
import {TaskStageAssignSelectTable} from './TaskAssignSelectTable';

interface TaskStageAssignContentProps extends TaskAssignButtonProps {
    form: FormInstance;
}

const TaskStageAssignContent = ({
    onFinish,
    form,
}: TaskStageAssignContentProps) => {
    const [taskCaseStats] = useTaskCaseStats();
    const taskID = useTaskTaskID();
    const username = Form.useWatch('username', form);
    const responsibleStageID = Form.useWatch('responsibleStageID', form);
    const caseIDs = Form.useWatch('caseIDs', form);
    const userList = Form.useWatch('userList', form);
    const stageOptions = useMemo(
        () => {
            return Object.values(taskCaseStats ?? {})?.map(item => ({
                label: item.stageName,
                value: item.stageID,
            }));
        },
        [taskCaseStats]
    );

    const {data, pending, refresh} = useRequest(apiTaskCasePureList, {
        taskID,
        username,
        responsibleStageID,
        pn: 1,
        size: 999999,
    });

    const handleFinish = useCallback(
        async (values: any) => {
            try {
                const {stageID, userList, caseIDs, allowUserRepeat} = values;
                const users: string[] = uniq(
                    userList?.reduce((acc: string[], cur: any) => {
                        if (cur?.username) {
                            acc.push(cur.username);
                        }
                        if (cur?.groupID) {
                            acc.push(...(cur?.usernameList || []));
                        }
                        return acc;
                    }, [])
                );
                const result = await apiTaskCaseDispatch({
                    taskID,
                    stageID,
                    userList: users,
                    caseIDs,
                    allowUserRepeat,
                });
                const details = [
                    result.finishedCaseIDS.length > 0
                        && `${result.finishedCaseIDS.length}条case已经完成不用处理`,
                    result.rejectedCaseIDS.length > 0
                        && `${result.rejectedCaseIDS.length}条case被打回无法更新`,
                    result.duplicateAssigneeCaseIDS.length > 0
                        && `${result.duplicateAssigneeCaseIDS.length}条case负责人重复无法分配`,
                ]
                    .filter(Boolean)
                    .join(',');
                message.success({
                    content: `批量分配任务成功，其中有${
                        result.successCaseIDS.length
                    }条case处理成功,${
                        caseIDs.length - result.successCaseIDS.length
                    }条case不满足批量处理条件已自动跳过${
                        details ? `（${details}）` : ''
                    }`,
                    duration: 10,
                });
                onFinish();
            } catch (error) {
                /* empty */
            } finally {
                refresh();
            }
        },
        [onFinish, refresh, taskID]
    );

    const [disabled, disabledReason] = useMemo(
        () => {
            let disabled = false;
            let disabledReason = '';
            if (!userList?.length) {
                disabled = true;
                disabledReason = '请选择分配人员或用户组';
            }
            if (!caseIDs?.length) {
                disabled = true;
                disabledReason = '请选择分配case';
            }
            return [disabled, disabledReason];
        },
        [caseIDs?.length, userList?.length]
    );
    return (
        <Form
            form={form}
            onFinish={handleFinish}
            initialValues={{
                stageID: head(stageOptions)?.value,
                allowUserRepeat: true,
            }}
            labelAlign="left"
            autoComplete="off"
            labelWrap
        >
            <Flex gap={16}>
                <Form.Item name="stageID" label="分配阶段" required>
                    <Select options={stageOptions} style={{width: 90}} />
                </Form.Item>
                <Form.Item
                    name="allowUserRepeat"
                    label="允许人员重复分配"
                    valuePropName="checked"
                >
                    <Switch />
                </Form.Item>
            </Flex>
            <Form.Item
                label="选择case并指定负责人"
                style={{width: '100%'}}
                required
            >
                <Flex gap={12}>
                    <Form.Item name="userList" noStyle>
                        <GroupUserSelect />
                    </Form.Item>
                    <Button
                        type="primary"
                        htmlType="submit"
                        icon={<TeamOutlined />}
                        disabledReason={disabledReason}
                        disabled={disabled}
                    >
                        分配
                    </Button>
                </Flex>
            </Form.Item>
            <Flex
                align="center"
                style={{
                    padding: '12px 14px',
                    width: '100%',
                    background: '#f5f5f5',
                }}
            >
                <span>Case筛选</span>
                <Form.Item
                    name="username"
                    label="负责人"
                    style={{marginBottom: 0}}
                >
                    <UserSelect style={{width: 250}} labelInValue={false} />
                </Form.Item>
                <Form.Item
                    name="responsibleStageID"
                    label="负责阶段"
                    style={{marginBottom: 0}}
                >
                    <Select
                        options={stageOptions}
                        style={{width: 120}}
                        placeholder="请输选择阶段"
                        allowClear
                    />
                </Form.Item>
            </Flex>
            <Form.Item name="caseIDs">
                <TaskStageAssignSelectTable
                    dataSource={data?.list}
                    pending={pending}
                />
            </Form.Item>
        </Form>
    );
};

export default TaskStageAssignContent;
