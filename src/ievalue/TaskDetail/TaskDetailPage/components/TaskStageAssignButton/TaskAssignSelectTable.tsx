import {Key} from 'react';
import {Table, Typography} from 'antd';
import {head, map, uniq} from 'lodash';
import {TaskCasePureItem} from '@/api/ievalue/task';
import {FormItemParamProps} from '@/types/common/form';

const selections = [
    Table.SELECTION_ALL,
    Table.SELECTION_INVERT,
    Table.SELECTION_NONE,
];

const getColumns = (dataSource: TaskCasePureItem[] = []) => {
    const L0Filters = uniq(map(dataSource, 'l0'))?.map(i => ({
        text: i,
        value: i,
    }));
    const stageList = head(dataSource)?.stageOperatorList || [];

    return [
        {
            title: '',
            dataIndex: '',
            width: 10,
            fixed: 'left',
        },
        {
            title: 'Query',
            dataIndex: 'query',
            render: (value: string) => {
                if (!value) {
                    return <></>;
                }
                return (
                    <Typography.Text
                        style={{
                            width: 260,
                        }}
                        ellipsis={{tooltip: value}}
                    >
                        {value}
                    </Typography.Text>
                );
            },
        },
        {
            title: 'L0',
            dataIndex: 'l0',
            width: 140,
            ellipse: true,
            filters: L0Filters,
            onFilter: (value: string, record: TaskCasePureItem) => {
                return record.l0.includes(value);
            },
        },
        ...stageList?.map((item, index) => {
            return {
                title: `${item.stageName}人`,
                dataIndex: ['stageOperatorList', index, 'username'],
                width: 140,
                ellipse: true,
            };
        }),
    ];
};

interface TaskStageAssignSelectTableProps extends FormItemParamProps<Key[]> {
    dataSource: TaskCasePureItem[];
    pending: boolean;
}

export const TaskStageAssignSelectTable = ({
    value,
    onChange,
    dataSource = [],
    pending,
}: TaskStageAssignSelectTableProps) => {
    const columns = getColumns(dataSource);
    return (
        <Table<TaskCasePureItem>
            rowKey="caseID"
            size="small"
            columns={columns as any}
            dataSource={dataSource}
            loading={pending}
            rowSelection={{
                selectedRowKeys: value,
                onChange,
                selections,
            }}
            scroll={{y: 'calc(100vh - 500px)', x: 'max-content'}}
        />
    );
};
