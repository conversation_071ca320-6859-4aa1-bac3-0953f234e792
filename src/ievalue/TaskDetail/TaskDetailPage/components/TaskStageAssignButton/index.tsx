import {TeamOutlined} from '@ant-design/icons';
import {Button} from '@panda-design/components';
import {Form} from 'antd';
import {Modal} from '@panda-design/components';
import {useBoolean} from 'huse';
import {useCallback, useEffect} from 'react';
import {CustomBoundary} from '@/components/ievalue/Boundary';
import {useSpaceCodeSafe} from '@/hooks/ievalue/spacePage';
import {loadSpaceUserGroupList} from '@/regions/ievalue/space/spaceUserGroupList';
import TaskStageAssignContent from './TaskStageAssignContent';

export interface TaskAssignButtonProps {
    onFinish: () => void;
}

export const TaskStageAssignButton = ({onFinish}: TaskAssignButtonProps) => {
    const [form] = Form.useForm();
    const [isButtonsVisible, {on: show, off: hide}] = useBoolean(false);
    const spaceCode = useSpaceCodeSafe();
    const handleOk = useCallback(
        () => {
            form.submit();
        },
        [form]
    );

    useEffect(
        () => {
            if (isButtonsVisible) {
                loadSpaceUserGroupList({spaceCode});
                form.resetFields();
            }
        },
        [form, isButtonsVisible, spaceCode]
    );
    return (
        <>
            <Button type="text" icon={<TeamOutlined />} onClick={show}>
                在线分配
            </Button>
            <Modal
                title="在线分配"
                open={isButtonsVisible}
                onOk={handleOk}
                onCancel={hide}
                footer={null}
                cancelText="取消"
                destroyOnHidden
                width={1100}
                centered
                maskClosable={false}
            >
                <CustomBoundary.Loading>
                    <TaskStageAssignContent form={form} onFinish={onFinish} />
                </CustomBoundary.Loading>
            </Modal>
        </>
    );
};
