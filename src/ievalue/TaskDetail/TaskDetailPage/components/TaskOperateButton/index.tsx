/* eslint-disable complexity */
import {Dropdown, Flex, MenuProps, Popover} from 'antd';
import {useCallback} from 'react';
import {Button} from '@panda-design/components';
import {
    TaskInfoItem,
    apiTaskAutoEvalCancel,
    apiTaskAutoEvalRetry,
    apiTaskPredictCancel,
    apiTaskPredictRetry,
} from '@/api/ievalue/task';
import {
    TaskStageEnum,
    TaskStatusEnum,
    TemplateStageTypesEnum,
} from '@/constants/ievalue/task';
import {MenuInfo} from '@/types/ievalue/common';
import {useIsYiYanSpace, useSpaceCodeSafe} from '@/hooks/ievalue/spacePage';
import {useTaskInfo, useTaskStageList} from '@/hooks/ievalue/task';
import {ReportUrl} from '@/links/ievalue/report';
import {StyledLink} from '../../BasicDetail';
import {AllCompareReportList} from '../../BasicDetail/AllCompareReportList';

enum RetryRangeEnum {
    ALL = 'ALL',
    FAIL = 'FAIL',
}

const getRetryDropdownItems = (taskInfo: TaskInfoItem): MenuProps['items'] => {
    return [
        {
            key: RetryRangeEnum.FAIL,
            label: '重试失败',
        },
        {
            key: RetryRangeEnum.ALL,
            label: '重试全部',
            disabled:
                !!taskInfo.dispatchType
                && [TaskStatusEnum.CANCEL, TaskStatusEnum.RETRY].includes(
                    taskInfo.stageStatus as TaskStatusEnum
                ),
        },
    ];
};

const OperateButton = () => {
    const spaceCode = useSpaceCodeSafe();
    const [taskInfo, taskInfoRefresh] = useTaskInfo();
    const [stageInfo] = useTaskStageList();
    const isYiYangSpace = useIsYiYanSpace();

    const handleCancelPredict = useCallback(
        async () => {
            try {
                await apiTaskPredictCancel({taskID: taskInfo.taskID});
            } catch (e) {
            /* empty */
            } finally {
                taskInfoRefresh();
            }
        },
        [taskInfo.taskID, taskInfoRefresh]
    );

    const handleRetryPredictClick: MenuProps['onClick'] = useCallback(
        async ({key, domEvent}: MenuInfo) => {
            domEvent?.stopPropagation();
            try {
                await apiTaskPredictRetry({
                    taskID: taskInfo.taskID,
                    retryRange: key as RetryRangeEnum,
                });
            } catch (e) {
                /* empty */
            } finally {
                taskInfoRefresh();
            }
        },
        [taskInfo.taskID, taskInfoRefresh]
    );

    const handleCancelAutoEval = useCallback(
        async () => {
            try {
                await apiTaskAutoEvalCancel({taskID: taskInfo.taskID});
            } catch (e) {
            /* empty */
            } finally {
                taskInfoRefresh();
            }
        },
        [taskInfo.taskID, taskInfoRefresh]
    );

    const handleRetryAutoEvalClick: MenuProps['onClick'] = useCallback(
        async ({key, domEvent}: MenuInfo) => {
            domEvent?.stopPropagation();
            try {
                await apiTaskAutoEvalRetry({
                    taskID: taskInfo.taskID,
                    retryRange: key as RetryRangeEnum,
                });
            } catch (e) {
                /* empty */
            } finally {
                taskInfoRefresh();
            }
        },
        [taskInfo.taskID, taskInfoRefresh]
    );

    switch (taskInfo.stage) {
        case TaskStageEnum.FINISHED:
        case TaskStageEnum.TERMINATED:
            return (
                stageInfo.map(e => e.stageType).join(',')
                    !== TemplateStageTypesEnum.NEW_PREDICTING && (
                    <Flex gap={8}>
                        <StyledLink
                            to={ReportUrl.ReportTaskDetail.toUrl({
                                spaceCode,
                                taskID: taskInfo.taskID,
                            })}
                            target="_blank"
                        >
                            <Button type="primary">查看报告</Button>
                        </StyledLink>
                        {!isYiYangSpace && (
                            <Popover
                                content={
                                    <div style={{width: '600px'}}>
                                        <AllCompareReportList />
                                    </div>
                                }
                            >
                                <Button>查看对比报告</Button>
                            </Popover>
                        )}
                    </Flex>
                )
            );
        case TaskStageEnum.PREDICTING:
            if (
                [TaskStatusEnum.WAITING, TaskStatusEnum.RUNNING].includes(
                    taskInfo.stageStatus as TaskStatusEnum
                )
            ) {
                return (
                    <Button type="default" onClick={handleCancelPredict}>
                        取消推理
                    </Button>
                );
            }
            if (
                [
                    TaskStatusEnum.CANCEL,
                    TaskStatusEnum.FAIL,
                    TaskStatusEnum.RETRY,
                ].includes(taskInfo.stageStatus as TaskStatusEnum)
            ) {
                return (
                    <Dropdown
                        menu={{
                            items: getRetryDropdownItems(taskInfo),
                            onClick: handleRetryPredictClick,
                        }}
                    >
                        <Button>重试</Button>
                    </Dropdown>
                );
            }
            return <></>;
        case TaskStageEnum.AUTOCHECK:
            if (
                [TaskStatusEnum.WAITING, TaskStatusEnum.RUNNING].includes(
                    taskInfo.stageStatus as TaskStatusEnum
                )
            ) {
                return (
                    <Button type="default" onClick={handleCancelAutoEval}>
                        取消检测
                    </Button>
                );
            }
            if (
                [
                    TaskStatusEnum.CANCEL,
                    TaskStatusEnum.FAIL,
                    TaskStatusEnum.RETRY,
                ].includes(taskInfo.stageStatus as TaskStatusEnum)
            ) {
                return (
                    <Dropdown
                        menu={{
                            items: getRetryDropdownItems(taskInfo),
                            onClick: handleRetryAutoEvalClick,
                        }}
                    >
                        <Button>重试</Button>
                    </Dropdown>
                );
            }
            return <></>;
        case TaskStageEnum.EVALUATING:
        case TaskStageEnum.AUTO_EVALUATE:
            return <></>;
        default:
            return <></>;
    }
};

export default OperateButton;
