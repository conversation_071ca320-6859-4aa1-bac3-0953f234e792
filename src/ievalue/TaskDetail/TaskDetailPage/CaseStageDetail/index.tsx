import {CaretRightOutlined} from '@ant-design/icons';
import {Space, Typography} from 'antd';
import {Button} from '@panda-design/components';
import {uniqueId} from 'lodash';
import {Link} from 'react-router-dom';
import {FlexLayout} from '@/components/ievalue/FlexLayout';
import {TaskStageEnum} from '@/constants/ievalue/task';
import {useSpaceCodeSafe} from '@/hooks/ievalue/spacePage';
import {
    useTaskCaseStats,
    useTaskInfo,
    useTaskTaskID,
} from '@/hooks/ievalue/task';
import {TaskUrl} from '@/links/ievalue/task';
import {PageEmpty} from '@/design/PandaEmpty/PageEmpty';
import TaskPieChartPanel from '../components/TaskPieChartPanel';
import {TaskStageAssignButton} from '../components/TaskStageAssignButton';
import CaseRandomTaskJumpByStageButton from './CaseRandomTaskJumpByStageButton';
import {StageCard} from './StageCard';
export const CaseStageDetail = () => {
    const [data, taskCaseStatsRefresh] = useTaskCaseStats();
    const spaceCode = useSpaceCodeSafe();
    const taskID = useTaskTaskID();
    const [taskInfo] = useTaskInfo();
    const caseStageList = Object.values(data) ?? [];
    return (
        <FlexLayout gap={8} direction="column" style={{width: '100%'}}>
            <FlexLayout justify="space-between" style={{width: '100%'}}>
                <Typography.Title
                    level={5}
                    style={{
                        padding: '7px',
                        background:
                            'linear-gradient(45deg, #f5f7fa, transparent)',
                        flex: '1 0',
                    }}
                >
                    任务阶段
                </Typography.Title>
                <Space>
                    <TaskStageAssignButton onFinish={taskCaseStatsRefresh} />
                    <CaseRandomTaskJumpByStageButton
                        stage={TaskStageEnum.EVALUATING}
                        onRefresh={taskCaseStatsRefresh}
                    />
                    <CaseRandomTaskJumpByStageButton
                        stage={TaskStageEnum.AUDITING_FORWARD}
                        onRefresh={taskCaseStatsRefresh}
                    />
                    <Link
                        to={TaskUrl.taskGroupDetail.toUrl({
                            spaceCode,
                            taskID,
                            groupID: 0,
                            stageID: 'all',
                        })}
                        target="_blank"
                    >
                        <Button type="link">查看所有Case</Button>
                    </Link>
                </Space>
            </FlexLayout>
            {taskInfo.stage === TaskStageEnum.NEW ? (
                <PageEmpty tip={'任务还在新建状态，请稍后'} />
            ) : (
                <>
                    <FlexLayout
                        gap={32}
                        justify="center"
                        align="start"
                        style={{width: '100%', padding: 20}}
                    >
                        {caseStageList.reduce(
                            (result: any[], item, index, list) => {
                                result.push(
                                    <StageCard key={uniqueId()} item={item} />
                                );
                                if (index !== list.length - 1) {
                                    result.push(
                                        <CaretRightOutlined
                                            style={{
                                                marginTop: 25,
                                                fontSize: 16,
                                            }}
                                        />
                                    );
                                }
                                return result;
                            },
                            []
                        )}
                    </FlexLayout>
                    <TaskPieChartPanel />
                </>
            )}
        </FlexLayout>
    );
};
