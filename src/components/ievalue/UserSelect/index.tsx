import {Select, Spin} from 'antd';
import type {SelectProps} from 'antd/es/select';
import debounce from 'lodash/debounce';
import {useCallback, useMemo, useRef, useState, ReactNode} from 'react';
import {apiGetUserByQuery} from '@/api/ievalue/user';
import {UserUnAssignedObj} from '@/constants/ievalue/task';
import {FlexLayout} from '../FlexLayout';
import {UserAvatar} from '../UserAvatar/UserAvatar';

export interface DebounceSelectProps<ValueType = any>
    extends Omit<SelectProps<ValueType | ValueType[]>, 'options' | 'children'> {
    fetchOptions: (search: string) => Promise<ValueType[]>;
    debounceTimeout?: number;
    defaultOptions?: ValueType[];
}
function DebounceSelect<
    ValueType extends {key?: string, label: ReactNode, value: string | number} = any
>({
    fetchOptions,
    defaultOptions = [],
    debounceTimeout = 800,
    labelInValue = true,
    ...props
}: DebounceSelectProps<ValueType>) {
    const [fetching, setFetching] = useState(false);
    const [options, setOptions] = useState<ValueType[]>(defaultOptions);
    const fetchRef = useRef(0);
    const debounceFetcher = useMemo(
        () => {
            const loadOptions = (value: string) => {
                fetchRef.current += 1;
                const fetchID = fetchRef.current;
                setOptions([]);
                setFetching(true);

                fetchOptions(value).then(newOptions => {
                    if (fetchID !== fetchRef.current) {
                        // for fetch callback order
                        return;
                    }
                    const resultOptions = newOptions?.length > 0 ? newOptions : defaultOptions;
                    setOptions(resultOptions);
                    setFetching(false);
                });
            };

            return debounce(loadOptions, debounceTimeout);
        },
        [debounceTimeout, fetchOptions, defaultOptions]
    );
    return (
        <Select
            showSearch
            labelInValue={labelInValue}
            filterOption={false}
            onSearch={debounceFetcher}
            notFoundContent={fetching ? <Spin size="small" /> : null}
            {...props}
            options={options}
        />
    );
}

async function fetchUserList(username: string): Promise<any> {
    if (username === '') {
        return [];
    }
    return apiGetUserByQuery({userID: username})
        .then(users => {
            return users.map(user => {
                return user;
            });
        })
        .catch((): UserItem[] => []);
}

export type Nil = undefined | null;

/**
 * 前端使用的账号类型
 */
export interface UserItem {
    departmentName: string;
    email: string;
    name: string;
    username: string;
}

interface UserSelectProps extends SelectProps {
    value?: string[];
    onChange?: (newValue: any, options?: any) => void;
    defaultOptions?: any[];
}

export function UserSelect(props: UserSelectProps) {
    const defaultOptionRender = useCallback(
        (option: any) => {
            return (
                <FlexLayout align="center">
                    <UserAvatar user={option.data?.username || ''} size={18} />
                    <div style={{height: '100%', marginLeft: 4}}>
                        {option?.data?.username === UserUnAssignedObj.label
                            ? UserUnAssignedObj.label
                            : `${option.data.name}(${option.data.username})-${option.data.departmentName}`}
                    </div>
                </FlexLayout>
            );
        },
        []
    );

    return (
        <DebounceSelect
            placeholder={props.placeholder || '请输入用户名称搜索'}
            fetchOptions={fetchUserList}
            fieldNames={{value: 'username', label: 'username'}}
            style={props.style || {width: '100%'}}
            optionRender={props.optionRender || defaultOptionRender}
            defaultOptions={props?.defaultOptions}
            allowClear={props?.allowClear ?? true}
            {...props}
        />
    );
}
