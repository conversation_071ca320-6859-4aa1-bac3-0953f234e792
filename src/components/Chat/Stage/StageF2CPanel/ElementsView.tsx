import styled from '@emotion/styled';
import {Element} from '../../Element';

interface ElementsViewProps {
    elements: any[];
    className?: string;
}

const Content = styled.div`
    flex: 1;
    padding: 16px 0;
    display: flex;
    flex-direction: column;
    gap: 8px;
    overflow-y: auto;
`;

export const ElementsView = ({elements, className}: ElementsViewProps) => {
    return (
        <Content className={className}>
            {elements.map((element, index) => (
                <Element key={index} item={element} />
            ))}
        </Content>
    );
};
