/**
 * @file 提交历史页和对比页的 diff view
 */
import {useCallback, useEffect, useMemo} from 'react';
import {css} from '@emotion/css';
import {useFullscreen} from '@/design/icode/Fullscreen';
import {initRawDiffHunks} from '@/third-party/devops-code/regions/diff';
import {DiffProvider} from '@/third-party/devops-code/providers/DiffProvider';
import {DiffViewWithFileMode} from '@/third-party/devops-code/CommitDiff';
import {useCurrentRepoName} from '@/regions/icode/currentRepo';
import DiffViewHeader from '@/components/icode/DiffViewHeader';
import {TreeLayout} from '@/design/icode/ResizeLayout/TreeLayout';
import {myToken} from '@/constants/colors';
import DiffTree from './DiffTree';
import {getFileList, loadFileList, useFileList} from './regions';

const containerCss = css`
    border-top: ${myToken.border};
    margin: 0 -20px -20px -20px;
    display: flex;
`;

const fullscreenCss = css`
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: calc(100vh - 48px);
    display: flex;
    background-color: white;
`;

interface Props {
    toCommitId: string;
    fromCommitId: string;
    shouldLoadFileList?: boolean;
    path?: string;
    onPathChange: (path: string) => void;
    compareMode?: 'merge-base' | 'direct';
    mergeBaseCommitId?: string;
}

// 一开始 from 和 to 搞错过，应该总是 to...from 比如 to HEAD~ from HEAD，表示从现在开始，对比指向上一个
const FileChange = ({
    toCommitId,
    fromCommitId,
    shouldLoadFileList = true,
    path = '',
    onPathChange,
    compareMode = 'merge-base',
    mergeBaseCommitId = '', // 如果是 merge-base 模式，需要传入 merge-base 的 commitId
}: Props) => {
    const repoName = useCurrentRepoName();
    const fullscreen = useFullscreen();
    const fileList = useFileList();

    const updatePath = useCallback(
        (path: string) => {
            onPathChange(path);
            // 如果是merge-base形式的对比，diff的是merge-base和from的内容，
            // 所以oldCommitId不是直接传toCommitId，而是toCommitId和fromCommitId的merge-base
            initRawDiffHunks({
                repo: repoName,
                oldCommitId: (compareMode === 'merge-base' && mergeBaseCommitId) ? mergeBaseCommitId : toCommitId,
                newCommitId: fromCommitId,
                fileName: path,
            });
        },
        [fromCommitId, onPathChange, repoName, toCommitId, mergeBaseCommitId, compareMode]
    );

    useEffect(
        () => {
            const effect = async () => {
                if (shouldLoadFileList) {
                    await loadFileList({repo: repoName, aCommitId: fromCommitId, bCommitId: toCommitId, compareMode});
                }
                const fileList = getFileList();
                if (fileList.find(item => item.fileName === path)) {
                    // do nothing
                }
                else {
                    const nextPath = fileList[0]?.fileName ?? '';
                    onPathChange(nextPath);
                }
            };
            effect();
        },
        // 与旧代码保持一致
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [toCommitId, fromCommitId, repoName, compareMode]
    );

    // 对比页是不需要 comments 的
    const context = useMemo(
        () => ({
            repoName,
            path,
            oldCommitId: (compareMode === 'merge-base' && mergeBaseCommitId) ? mergeBaseCommitId : toCommitId,
            newCommitId: fromCommitId,
            canReply: true,
        }),
        [repoName, path, toCommitId, fromCommitId, compareMode, mergeBaseCommitId]
    );

    return (
        <div className={fullscreen ? fullscreenCss : containerCss}>
            <TreeLayout
                left={(
                    <DiffTree path={path} bCommitId={fromCommitId} onSelect={updatePath} />
                )}
                center={(
                    <DiffProvider value={context}>
                        <DiffViewHeader
                            fileList={fileList}
                            onPathSelect={onPathChange}
                        />
                        <DiffViewWithFileMode />
                    </DiffProvider>
                )}
            />
        </div>
    );
};

export default FileChange;
