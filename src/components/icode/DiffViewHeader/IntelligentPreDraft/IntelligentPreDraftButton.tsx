import {useMemo, useCallback, useRef} from 'react';
import {Button} from '@panda-design/components';
import styled from '@emotion/styled';
import {isNumber, isArray, differenceBy} from 'lodash';
import {Tooltip} from 'antd';
import {useCurrentRepoName} from '@/regions/icode/currentRepo';
import {useDiffContext} from '@/third-party/devops-code/providers/DiffProvider';
import reviewPng from '@/assets/icode/review.png';
import {useChangeInfo, useCurrentPatchSets} from '@/regions/icode/review';
import {
} from '@/regions/icode/dataset';
import {
    // setIntelligentCommentTour,
    useDeletedIntelligentPreDraft,
    // useIntelligentCommentTour,
} from '@/regions/icode/localStorage';
import {IconPlay} from '@/icons-icode/actions';
import {
    loadFileIntelligentPreDraftResult,
    useFileIntelligentPreDraftResult,
    useFileIntelligentPreDraftResultLoading,
} from '@/regions/icode/review/intelligentReview';
import {intelligentColors} from '@/constants/colors/icode';

const StyledButton = styled(Button)`
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px !important;
    line-height: 24px;
    background: ${intelligentColors.reviewButtonBackground} !important;
    border: none !important;
    border-radius: 12px 0 0 12px !important;
`;

const StyledImg = styled.img`
    width: 16px;
    height: 16px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 2px;
`;

const StyledDiv = styled.div`
    display: flex;
    align-items: center;
`;

const StyledJumpButton = styled(Button)`
    display: flex;
    align-items: center;
    padding: 0 !important;
    height: 24px;
    color: var(--color-gray-7) !important;
    &:hover {
        color: ${intelligentColors.reviewButtonColor} !important;
    }
    svg {
        width: 12px !important;
        height: 12px !important;
    }
`;

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const IntelligentPreDraftButton = ({openTour}: {openTour?: () => void}) => {
    const repo = useCurrentRepoName();
    const {enableComment, path, oldCommitId, newCommitId} = useDiffContext();
    const {bPatchSet} = useCurrentPatchSets();
    const {changeNumber} = useChangeInfo();
    const scrollIntoRef = useRef<string>('');
    // const showTourIntelligentComment = useIntelligentCommentTour();

    const regionParams = useMemo(
        () => ({
            patchSetId: bPatchSet.patchSetId,
            repo,
            changeNumber,
            path,
            aCommitId: oldCommitId,
            bCommitId: newCommitId,
        }),
        [oldCommitId, newCommitId, bPatchSet.patchSetId, changeNumber, path, repo]
    );
    const fetchLoading = useFileIntelligentPreDraftResultLoading(regionParams);
    const intelligentPreDraft = useFileIntelligentPreDraftResult(regionParams);
    const {deletedIntelligentPreDraft} = useDeletedIntelligentPreDraft();

    const tipText = useMemo(
        () => {
            if (fetchLoading) {
                return '智能评审中···';
            }
            if (isArray(intelligentPreDraft)) {
                const result = differenceBy(intelligentPreDraft, deletedIntelligentPreDraft, 'id');
                if (result.length === 0) {
                    return '小码哥：未发现问题，为您点赞！';
                }
                // if (showTourIntelligentComment) {
                //     setTimeout(() => {
                //         openTour();
                //         setIntelligentCommentTour(false);
                //     }, 500);
                // }
                return `智能评审(${result.length})`;
            }
            return '点击智能评审';
        },
        [deletedIntelligentPreDraft, fetchLoading, intelligentPreDraft]
    );

    const handleClickFileIntelligent = useCallback(
        () => {
            if (isNumber(bPatchSet.patchSetId) && oldCommitId && newCommitId) {
                loadFileIntelligentPreDraftResult({
                    ...regionParams,
                    triggerMode: 'MANUAL',
                });
            }
        },
        [oldCommitId, newCommitId, bPatchSet.patchSetId, regionParams]
    );

    const handleGo = useCallback(
        (direction: 'prev' | 'next') => {
            if (!isArray(intelligentPreDraft)) {
                return;
            }
            const currentIndex = intelligentPreDraft.findIndex(item => item.id === scrollIntoRef.current);
            if (direction === 'prev') {
                scrollIntoRef.current = (currentIndex - 1) > -1
                    ? intelligentPreDraft[currentIndex - 1].id : intelligentPreDraft[0].id;
            }
            else {
                scrollIntoRef.current = (currentIndex + 1 < intelligentPreDraft.length)
                    ? intelligentPreDraft[currentIndex + 1].id : intelligentPreDraft[intelligentPreDraft.length].id;
            }
            document.getElementById(scrollIntoRef.current)?.scrollIntoView({behavior: 'smooth', block: 'center'});
        },
        [intelligentPreDraft]
    );

    if (!enableComment) {
        return null;
    }

    return (
        <StyledButton size="small">
            {/* <Tooltip title="模型努力升级中，期间对生成效果有较大影响，建议升级完成后再使用，抱歉给您的使用带来不便～"> */}
            <StyledDiv onClick={handleClickFileIntelligent}>
                <StyledImg src={reviewPng} />
                <span style={{color: intelligentColors.reviewButtonColor}}>{tipText}</span>
            </StyledDiv>
            {/* </Tooltip> */}
            {tipText.startsWith('智能评审(') && (
                <div style={{display: 'flex', gap: '4px'}}>
                    <Tooltip title="查看上一个结果">
                        <StyledJumpButton type="link" onClick={() => handleGo('prev')}>
                            <IconPlay style={{transform: 'rotate(180deg)'}} />
                        </StyledJumpButton>
                    </Tooltip>
                    <Tooltip title="查看下一个结果">
                        <StyledJumpButton type="link" onClick={() => handleGo('next')}>
                            <IconPlay />
                        </StyledJumpButton>
                    </Tooltip>
                </div>
            )}
        </StyledButton>
    );
};
