import {useMemo} from 'react';
import {get, groupBy, uniq} from 'lodash';
import styled from '@emotion/styled';
import {fontSize, marginRight} from '@panda-design/components';
import {TOOL_CLASSES} from '@/constants/icode/checkers';
import reviewPng from '@/assets/icode/review.png';
import {Comment} from '@/types/icode/comment';
import CommentRangeInfo from '@/components/icode/CommitList/CommentRangeInfo';
import CommentRiskTag from './CommentRiskTag';

const TitleSpan = styled.span`
    display: flex;
    align-items: center;
    line-height: 25px;
    color: var(--color-gray-10);
`;

const StyledImg = styled.img`
    width: 16px;
    height: 16px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 4px;
`;

interface Props {
    comments: Comment[];
    isIntelligent: boolean;
}

export const CommentTitleSpan = ({comments, isIntelligent}: Props) => {
    const titleSpan = useMemo(
        () => {
            const {
                true: unPublishedComments = [],
                false: publishedComments = [],
            } = groupBy(comments, comment => comment.type === 'DRAFT' || comment.type === 'NEW');
            const rangeKey = get(comments, '[0].rangeKey');
            const lineInfoElement = (
                <>
                    {
                        rangeKey
                            ? <CommentRangeInfo rangKey={rangeKey} />
                            : <span className={fontSize(12)}>{get(comments, '[0].line', '')}</span>
                    }
                </>
            );
            if (isIntelligent) {
                return (
                    <TitleSpan>
                        <TitleSpan className={marginRight(4)}>
                            <StyledImg src={reviewPng} />
                            数字员工智能评审
                        </TitleSpan>
                        {lineInfoElement}
                        <CommentRiskTag commentRiskType={comments[0].commentRiskType} />
                    </TitleSpan>
                );
            }
            const authors: string[] = publishedComments.map(comment => {
                const {type, messageInfo, author} = comment;
                if (type === 'ROBOT') {
                    const classify = messageInfo ? messageInfo.classify : '';
                    // @ts-expect-error
                    return TOOL_CLASSES[classify] || '机器检查';
                }
                return author ?? '草稿';
            });
            const uniqAuthors = uniq(authors);
            const texts = [];
            if (publishedComments.length > 0) {
                const haveRob = publishedComments.some(ref => ref?.type === 'ROBOT');
                const textsPre = haveRob ? `${uniqAuthors.join('，')}，` : '';
                texts.push(`${textsPre}${uniqAuthors.length}人参与评论，共计${publishedComments.length}条评审`);
            }
            if (unPublishedComments.length > 0) {
                texts.push(`${unPublishedComments.length}条草稿`);
            }
            const title = texts.join('，');
            return (
                <TitleSpan>
                    <span className={marginRight(4)}>{title}</span>
                    {lineInfoElement}
                </TitleSpan>
            );
        },
        [comments, isIntelligent]
    );

    return titleSpan;
};
