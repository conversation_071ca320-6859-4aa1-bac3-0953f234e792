import {Button} from '@panda-design/components';
import styled from '@emotion/styled';
import {useEnableDirectPublishComment} from '@/regions/icode/reviewSettings';
import {Comment, CommentType} from '@/types/icode/comment';
import {IconEdit, IconReply, IconDelete, IconPublish} from '@/icons-icode/lucide';
import useCommentHandlers from './useCommentHandlers';
import useIntelligentCommentHandlers from './useIntelligentCommentHandlers';
import IntelligentOperationButton from './IntelligentOperationButton';
import {ReviewStaff} from './ReviewStaff';

const Operation = styled.div`
    padding-left: 24px;
    display: flex;
    align-items: center;
    gap: 4px;
    padding-top: 4px;
`;

interface Props {
    comment: Comment;
}

const OperationButton = ({comment}: Props) => {
    const enableDirectPublishComment = useEnableDirectPublishComment();

    const {
        handleEdit,
        handlePublish,
        handleDelete,
        handleReply,
    } = useCommentHandlers(comment);

    const {
        handleAddIntelligentDraft,
        handleRemoveNewIntelligentComment,
    } = useIntelligentCommentHandlers(comment);

    const {type, commentType} = comment;
    const isPublished = type !== 'DRAFT' && type !== 'NEW';
    const isNewIntelligent = type === 'NEW_INTELLIGENT';

    if (isNewIntelligent) {
        return (
            <IntelligentOperationButton
                onAddIntelligentDraft={handleAddIntelligentDraft}
                onRemoveNewIntelligentComment={handleRemoveNewIntelligentComment}
            />
        );
    }

    if (isPublished) {
        return (
            <Operation>
                <Button
                    size="small"
                    type="text"
                    icon={<IconReply />}
                    onClick={handleReply}
                >
                    回复
                </Button>
                {commentType === CommentType.ICODE_LITTLE_K && <ReviewStaff />}
            </Operation>
        );
    }
    return (
        <Operation>
            {enableDirectPublishComment && (
                <>
                    <Button
                        size="small"
                        type="text"
                        icon={<IconPublish />}
                        onClick={handlePublish}
                    >
                        发表
                    </Button>
                    <span>|</span>
                </>
            )}
            <Button
                size="small"
                type="text"
                icon={<IconEdit />}
                onClick={handleEdit}
            >
                编辑
            </Button>
            <span>|</span>
            <Button
                size="small"
                type="text"
                icon={<IconDelete />}
                onClick={handleDelete}
            >
                删除
            </Button>
            {commentType === CommentType.ICODE_LITTLE_K && <ReviewStaff />}
        </Operation>
    );
};

export default OperationButton;
