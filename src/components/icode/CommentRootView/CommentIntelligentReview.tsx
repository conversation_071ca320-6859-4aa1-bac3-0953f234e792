import {use<PERSON><PERSON>back, SetStateAction, useState, useEffect, useRef} from 'react';
import styled from '@emotion/styled';
import {isEmpty, isUndefined} from 'lodash';
import {Button, message} from '@panda-design/components';
import {log} from '@baidu/k-log';
import {INTELLIGENT_CODE_FLAG} from '@/constants/icode/review';
import {getRawText} from '@/third-party/devops-code/regions/text';
import {useDiffContext} from '@/third-party/devops-code/providers/DiffProvider';
import {useCurrentRepoName} from '@/regions/icode/currentRepo';
import useReviewTrack from '@/hooks/icode/review/useReviewTrack';
import {Comment, CommentType} from '@/types/icode/comment';
import {TextAreaCursorAndValueInfo, DebouncedFunc} from '@/types/icode/input';
import {getTextAreaCursorInfo} from '@/utils/icode/input';
import {apiPostTrackIntelligentReview} from '@/api/icode/review';
import {apiGetCodeIntelligentReview} from '@/api/icode/ai';
import reviewPng from '@/assets/icode/review.png';
import {useCurrentUserName} from '@/regions/icode/currentUser';
import {intelligentColors} from '@/constants/colors/icode';
import {setIntelligentCommentCount, getIntelligentCommentCount, setIntelligentCommentMessageIdAndUuid} from './regions';

export interface Loading {
    status: boolean;
    type?: ButtonText;
}

const Layout = styled.div`
    width: 82px;
    height: 24px;
    border-radius: 12px;
    background: var(--color-brand-1);
    font-size: 14px;
    display: flex;
    column-gap: 4px;
    color: var(--color-brand-6);
    justify-content: center;
    align-items: center;
    min-width: 50px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
`;

const StyledButton = styled(Button)`
    display: flex;
    align-items: center;
    font-size: 12px !important;
    line-height: 24px;
    border-radius: 12px !important;
    background: ${intelligentColors.reviewButtonBackground} !important;
    border: none !important;
    color: ${intelligentColors.reviewButtonColor} !important;
`;

const StyledImg = styled.img`
    width: 12px;
    height: 12px;
    margin-right: 4px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 4px;
`;

export type ButtonText = '智能评审' | '终止生成' | '撤销生成';

interface Props {
    textAreaData: TextAreaCursorAndValueInfo;
    comment: Comment;
    setTextAreaData: (value: SetStateAction<TextAreaCursorAndValueInfo>) => void;
    changeHistoryDataList: DebouncedFunc<(textAreaData: TextAreaCursorAndValueInfo) => void>;
    setLoading: (value: Loading) => void;
}

const CommentIntelligentReview = ({
    comment,
    setTextAreaData,
    changeHistoryDataList,
    textAreaData,
    setLoading,
}: Props) => {
    const [buttonText, setButtonText] = useState<ButtonText>('智能评审');
    const reviewTrack = useReviewTrack();
    // 用于给终止生成使用的拦截flag
    const stopIntelligentCommentRef = useRef(true);
    // 记录当前智能评审有多少个 只要大于1 就会算作采纳
    const intelligentCommentCountRef = useRef(0);
    // 设置智能评审前的评论，用于给撤销用的
    const [originComment, setOriginComment] = useState<string>();
    // 评论为智能评审时候，记录一下
    const [intelligentComment, setIntelligentComment] = useState<string>();
    const repo = useCurrentRepoName();
    const {path, oldCommitId, newCommitId} = useDiffContext();
    const {side, line, range} = comment;
    const currentUsername = useCurrentUserName();

    const onCreateIntelligentComment = useCallback(
        // eslint-disable-next-line max-statements
        async () => {
            try {
                reviewTrack('智能评审', {placeOfUse: 'line', intelligentReviewAction: '点击生成按钮'});
                const {changeId: changeNumber, patchSetId, path: commentPath, id: commentId} = comment;

                stopIntelligentCommentRef.current = true;
                setLoading({status: true, type: '智能评审'});
                let startLine = line - 1;
                let endLine = line;
                if (range) {
                    startLine = range.startLine - 1;
                    endLine = range.endLine;
                }
                const currentFile = getRawText({
                    repo,
                    commitId: side === 'PARENT' ? oldCommitId : newCommitId,
                    filePath: path,
                });
                const lines = currentFile?.lines ?? [];
                const content = lines.slice(startLine, endLine).join('\n');
                if (!content) {
                    setLoading({status: false});
                    message.info('请选择有效代码行进行智能评审');
                    return;
                }
                setButtonText('终止生成');
                const intelligentReviewResult = await apiGetCodeIntelligentReview({repo, path, content});

                if (isEmpty(intelligentReviewResult)) {
                    setLoading({status: false});
                    setButtonText('智能评审');
                    message.info('这块代码写的很棒，小码哥没找到问题～');
                }
                else if (stopIntelligentCommentRef.current) {
                    intelligentCommentCountRef.current++;
                    setIntelligentCommentCount({commentId}, intelligentCommentCountRef.current);
                    reviewTrack('智能评审', {placeOfUse: 'line', intelligentReviewAction: '生成评论'});
                    setTextAreaData(({value, selectionEnd}: TextAreaCursorAndValueInfo) => {
                        const commentWordsArr = value ? value.split('\n') : [];
                        const cursorInfo = getTextAreaCursorInfo(commentWordsArr.join('\n'), selectionEnd);
                        intelligentReviewResult.forEach(result => {
                            const {advice, adviceCode, uuid, messageId} = result;
                            commentWordsArr.push(advice);

                            apiPostTrackIntelligentReview({
                                changeNumber,
                                patchSetId,
                                uuid,
                                username: currentUsername,
                                path: commentPath,
                                type: 'ICODE_LITTLE_K_READ',
                                source: 'LINE',
                                sendTime: Date.now(),
                            });

                            setIntelligentCommentMessageIdAndUuid({commentId}, {messageId, uuid});
                            log({
                                action: 'read',
                                source: 'k_chat_code_review_line',
                                messageId,
                                description: advice,
                            });

                            !!adviceCode && commentWordsArr.push(INTELLIGENT_CODE_FLAG, adviceCode, '```');
                        });
                        const finalCommentValue = commentWordsArr.join('\n');
                        const finalTextAreaData = {...cursorInfo, value: finalCommentValue};
                        setOriginComment(value);
                        setIntelligentComment(finalCommentValue);
                        changeHistoryDataList(finalTextAreaData);
                        setLoading({status: false});
                        setTimeout(() => setButtonText('撤销生成'), 0);
                        return finalTextAreaData;
                    });
                }
                else {
                    setLoading({status: false});
                    stopIntelligentCommentRef.current = true;
                }
            }
            catch (e) {
                setButtonText('智能评审');
                setLoading({status: false});
                message.error(e.message);
            }
        },
        [
            setLoading,
            currentUsername,
            line,
            range,
            repo,
            side,
            oldCommitId,
            newCommitId,
            path,
            reviewTrack,
            setTextAreaData,
            changeHistoryDataList,
            comment,
        ]
    );

    const onStopIntelligentComment = useCallback(
        () => {
            if (!isUndefined(originComment)) {
                setLoading({status: true, type: '撤销生成'});
                intelligentCommentCountRef.current--;
                // 记录region 方便评论在保存取消时候打点是否采纳了智能评审
                setIntelligentCommentCount({commentId: comment.id}, intelligentCommentCountRef.current);
                reviewTrack('智能评审', {placeOfUse: 'line', intelligentReviewAction: '点击撤销生成按钮'});
                setButtonText('智能评审');
                const commentWordsArr = originComment ? originComment.split('\n') : [];
                const cursorInfo = getTextAreaCursorInfo(commentWordsArr.join('\n'), originComment.length - 1);
                setTextAreaData({...cursorInfo, value: originComment});
                setTimeout(() => setLoading({status: false}), 100);
            }
        },
        [comment.id, originComment, reviewTrack, setLoading, setTextAreaData]
    );

    const onRepealIntelligentComment = useCallback(
        () => {
            if (!isUndefined(originComment)) {
                setLoading({status: true, type: '终止生成'});
                intelligentCommentCountRef.current--;
                setIntelligentCommentCount({commentId: comment.id}, intelligentCommentCountRef.current);
                reviewTrack('智能评审', {placeOfUse: 'line', intelligentReviewAction: '点击终止生成按钮'});

                setButtonText('智能评审');
                const commentWordsArr = originComment ? originComment.split('\n') : [];
                const cursorInfo = getTextAreaCursorInfo(commentWordsArr.join('\n'), originComment.length - 1);
                setTextAreaData({...cursorInfo, value: originComment});
                setTimeout(() => setLoading({status: false}), 100);
            }
        },
        [comment.id, originComment, reviewTrack, setLoading, setTextAreaData]
    );

    const handleOnClick = useCallback(
        async () => {
            if (buttonText === '智能评审') {
                onCreateIntelligentComment();
            }
            else if (buttonText === '撤销生成') {
                onStopIntelligentComment();
            }
            else if (buttonText === '终止生成') {
                onRepealIntelligentComment();
            }
        },
        [buttonText, onCreateIntelligentComment, onStopIntelligentComment, onRepealIntelligentComment]
    );

    useEffect(
        () => {
            if (textAreaData.value === '') {
                intelligentCommentCountRef.current = 0;
                setIntelligentCommentCount({commentId: comment.id}, 0);
                setButtonText('智能评审');
            }
        },
        [comment.id, textAreaData.value]
    );

    useEffect(
        () => {
            // 组件ready后进行一次是否是小k评论的标记初始化
            const value = textAreaData.value || '';
            const {type, commentType} = comment;
            // 如果是智能评审但是是新评论 说明是刚复制的父评论的 不算智能评审
            const initCount = commentType === CommentType.ICODE_LITTLE_K && type !== 'NEW' ? 1 : 0;
            // 看看初始化的内容是否带有智能评审标记
            const intelligentCommentCount = value.split(INTELLIGENT_CODE_FLAG).length - 1;
            // 用户已经手动进行过的智能评审生成操作 因为切换文件  再次回来还会触发这里 所以用户有可能操作过但没保存
            const alreadyIntelligentCommentCount = getIntelligentCommentCount({commentId: comment.id});

            setIntelligentCommentCount(
                {commentId: comment.id},
                Math.max(initCount, intelligentCommentCount, alreadyIntelligentCommentCount)
            );
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [comment.id]
    );

    useEffect(
        () => {
            const nowComment = textAreaData.value;
            // eslint-disable-next-line max-len
            if (buttonText === '撤销生成' && (![intelligentComment, originComment].includes(nowComment) || nowComment === '')) {
                setButtonText('智能评审');
            }
        },
        [buttonText, intelligentComment, originComment, textAreaData.value, comment.id]
    );

    return (
        <Layout>
            <StyledButton size="small" onClick={handleOnClick}>
                <StyledImg src={reviewPng} />
                {buttonText}
            </StyledButton>
        </Layout>
    );
};

export default CommentIntelligentReview;
