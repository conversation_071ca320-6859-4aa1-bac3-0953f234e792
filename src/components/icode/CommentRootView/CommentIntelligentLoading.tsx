import {useMemo} from 'react';
import styled from '@emotion/styled';
import reviewPng from '@/assets/icode/review.png';
import {Loading} from './CommentIntelligentReview';

const Layout = styled.div`
    position: absolute;
    bottom: -40px;
    left: 0;
    height: 32px;
    display: flex;
    align-items: center;
    color: var(--color-brand-6);
`;

const StyledImg = styled.img`
    width: 16px;
    height: 16px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 4px;
`;

export type ButtonText = '智能评审' | '终止生成' | '撤销生成';

interface Props {
    loading: Loading;
}

const CommentIntelligentLoading = ({loading}: Props) => {
    const loadingText = useMemo(
        () => {
            if (loading.type === '智能评审') {
                return '数字员工小码哥正在思考···';
            }
            return '';
        },
        [loading.type]
    );

    if (!loading.status || loading.type !== '智能评审') {
        return null;
    }

    return (
        <Layout>
            <StyledImg src={reviewPng} />
            {loadingText}
        </Layout>
    );
};

export default CommentIntelligentLoading;
