
import {useCallback, SetStateAction, useState, useEffect, useRef, useMemo} from 'react';
import styled from '@emotion/styled';
import {isEmpty} from 'lodash';
import {message} from '@panda-design/components';
import {Input} from 'antd';
import {useBoolean} from 'huse';
import {INTELLIGENT_CODE_FLAG} from '@/constants/icode/review';
import {getRawText} from '@/third-party/devops-code/regions/text';
import {useDiffContext} from '@/third-party/devops-code/providers/DiffProvider';
import {useCurrentRepoName} from '@/regions/icode/currentRepo';
import useReviewTrack from '@/hooks/icode/review/useReviewTrack';
import {Comment, CommentType} from '@/types/icode/comment';
import {TextAreaCursorAndValueInfo, DebouncedFunc} from '@/types/icode/input';
import {IntelligentCodeReviewResult} from '@/types/icode/ai';
import {getTextAreaCursorInfo} from '@/utils/icode/input';
import {apiGetCodeIntelligentReview} from '@/api/icode/ai';
import reviewPng from '@/assets/icode/review.png';
import intelligentReviewBackground from '@/icons-icode/review/intelligentReviewBackground.svg';
import {setIntelligentCommentCount, getIntelligentCommentCount} from './regions';
import IntelligentOperationButton from './IntelligentOperationButton';

const Layout = styled.div`
    padding: 12px;
    border-radius: 4px;
    font-size: 14px;
    display: flex;
    column-gap: 4px;
    color: var(--color-brand-6);
    flex-flow: column;
    justify-content: center;
    align-items: flex-start;
    min-width: 50px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    background-image: url(${intelligentReviewBackground});
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-attachment: fixed;
`;

const Header = styled.div`
    display: flex;
    align-items: center;
    justify-content: center;
`;

const IntelligentCommentContent = styled.div`
    display: flex;
    margin-top: 8px;
    flex-flow: column;
    width: 100%;
`;

const StyledImg = styled.img`
    width: 16px;
    height: 16px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 4px;
`;

const StyledTextArea = styled(Input.TextArea)`
    background: #FFF !important;
    color: #000000 !important;
    border: 0;
    border-radius: 4px;
    padding: 8px;
`;

interface Props {
    textAreaData: TextAreaCursorAndValueInfo;
    comment: Comment;
    setTextAreaData: (value: SetStateAction<TextAreaCursorAndValueInfo>) => void;
    changeHistoryDataList: DebouncedFunc<(textAreaData: TextAreaCursorAndValueInfo) => void>;
}

const CommentIntelligentReview = ({textAreaData, comment, setTextAreaData, changeHistoryDataList}: Props) => {
    const reviewTrack = useReviewTrack();
    // 记录当前智能评论有多少个 只要大于1 就会算作采纳
    const intelligentCommentCountRef = useRef(0);
    const [emptyMessage, setEmptyMessage] = useState('');
    // 评论为智能评论时候，记录一下
    const [intelligentReviewResult, setIntelligentReviewResult] = useState<IntelligentCodeReviewResult[]>();
    const repo = useCurrentRepoName();
    const {path, oldCommitId, newCommitId} = useDiffContext();
    const {side, line, range} = comment;

    const [loading, {on: showLoading, off: hideLoading}] = useBoolean(false);
    const [visible, {off: hideIntelligentReview}] = useBoolean(true);

    const onCreateIntelligentComment = useCallback(
        async () => {
            try {
                showLoading();
                let startLine = line - 1;
                let endLine = line;
                if (range) {
                    startLine = range.startLine - 1;
                    endLine = range.endLine;
                }
                const currentFile = getRawText({
                    repo,
                    commitId: side === 'PARENT' ? oldCommitId : newCommitId,
                    filePath: path,
                });
                const lines = currentFile?.lines ?? [];
                const content = lines.slice(startLine, endLine).join('\n');
                if (!content) {
                    hideLoading();
                    setEmptyMessage('请选择有效代码行进行智能评论');
                    return;
                }
                const intelligentReviewResult = await apiGetCodeIntelligentReview({repo, path, content});
                if (isEmpty(intelligentReviewResult)) {
                    hideLoading();
                    setEmptyMessage('这块代码写的很棒，Comate没找到问题～');
                }
                else {
                    hideLoading();
                    setIntelligentReviewResult(intelligentReviewResult);
                }
            }
            catch (e) {
                hideLoading();
                message.error(e.message);
            }
        },
        [showLoading, line, range, repo, side, oldCommitId, newCommitId, path, hideLoading]

    );

    useEffect(
        () => {
            if (textAreaData.value === '') {
                intelligentCommentCountRef.current = 0;
                setIntelligentCommentCount({commentId: comment.id}, 0);
            }
        },
        [comment.id, textAreaData.value]
    );

    useEffect(
        () => {
            // 组件ready后进行一次是否是小k评论的标记初始化
            const value = textAreaData.value || '';
            const {type, commentType} = comment;
            // 如果是智能评论但是是新评论 说明是刚复制的父评论的 不算智能评论
            const initCount = commentType === CommentType.ICODE_LITTLE_K && type !== 'NEW' ? 1 : 0;
            // 看看初始化的内容是否带有智能评论标记
            const intelligentCommentCount = value.split(INTELLIGENT_CODE_FLAG).length - 1;
            // 用户已经手动进行过的智能评论生成操作 因为切换文件  再次回来还会触发这里 所以用户有可能操作过但没保存
            const alreadyIntelligentCommentCount = getIntelligentCommentCount({commentId: comment.id});
            setIntelligentCommentCount(
                {commentId: comment.id},
                Math.max(initCount, intelligentCommentCount, alreadyIntelligentCommentCount)
            );
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [comment.id]
    );

    const handleAddIntelligentDraft = useCallback(
        () => {
            reviewTrack('智能评审', {placeOfUse: 'line', intelligentReviewAction: '已采纳'});
            setIntelligentCommentCount({commentId: comment.id}, 1);
            setTextAreaData(({value, selectionEnd}: TextAreaCursorAndValueInfo) => {
                const commentWordsArr = value ? value.split('\n') : [];
                const cursorInfo = getTextAreaCursorInfo(commentWordsArr.join('\n'), selectionEnd);
                intelligentReviewResult.forEach(result => {
                    const {advice, adviceCode} = result;
                    commentWordsArr.push(advice);
                    !!adviceCode && commentWordsArr.push(INTELLIGENT_CODE_FLAG, adviceCode, '```');
                });
                const finalCommentValue = commentWordsArr.join('\n');
                const finalTextAreaData = {...cursorInfo, value: finalCommentValue};
                changeHistoryDataList(finalTextAreaData);
                hideIntelligentReview();
                return finalTextAreaData;
            });
        },
        [
            changeHistoryDataList,
            comment.id,
            hideIntelligentReview,
            intelligentReviewResult,
            reviewTrack,
            setTextAreaData,
        ]
    );

    const handleRemoveNewIntelligentComment = useCallback(
        () => {
            reviewTrack('智能评审', {placeOfUse: 'line', intelligentReviewAction: '放弃'});
            hideIntelligentReview();
        },
        [hideIntelligentReview, reviewTrack]
    );

    const intelligentComment = useMemo(
        () =>
            intelligentReviewResult?.reduce((result, item) => {
                const {advice, adviceCode} = item;
                result.push(advice);
                !!adviceCode && result.push(INTELLIGENT_CODE_FLAG, adviceCode, '```');
                return result;
            }, [] as string[]).join('\n')
        ,
        [intelligentReviewResult]
    );

    useEffect(
        () => {
            onCreateIntelligentComment();
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        []
    );

    if (!visible) {
        return null;
    }

    return (
        <Layout>
            <Header>
                <StyledImg src={reviewPng} />
                {loading ? 'Comate正在为你生成评审建议, 请耐心等待···' : 'Comate智能评审'}
            </Header>
            {!loading && (
                <IntelligentCommentContent>
                    {intelligentComment ? (
                        <>
                            <StyledTextArea value={intelligentComment} disabled />
                            <IntelligentOperationButton
                                onAddIntelligentDraft={handleAddIntelligentDraft}
                                onRemoveNewIntelligentComment={handleRemoveNewIntelligentComment}
                            />
                        </>
                    ) : emptyMessage}
                </IntelligentCommentContent>
            )}
        </Layout>
    );
};

export default CommentIntelligentReview;
