import type { SVGProps } from "react";
const SvgTooltip = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 16 16"
        {...props}
    >
        <mask
            id="tooltip_svg__a"
            width={16}
            height={16}
            x={0}
            y={0}
            maskUnits="userSpaceOnUse"
            style={{
                maskType: "luminance",
            }}
        >
            <path fill="#fff" d="M0 0h16v16H0z" />
        </mask>
        <g fill="#BFBFBF" mask="url(#tooltip_svg__a)">
            <path d="M8 .969A7.031 7.031 0 1 1 8 15.03 7.031 7.031 0 0 1 8 .97m0 1.25A5.781 5.781 0 1 0 8 13.78 5.781 5.781 0 0 0 8 2.22" />
            <path d="M7.359 10.748h1.25v1.25h-1.25zM7.358 9.818c-.007-.868.407-1.605 1.19-2.168l.023-.017.024-.018.026-.014a1.25 1.25 0 1 0-1.87-1.106l-.001.02H5.5a2.5 2.5 0 1 1 3.79 2.143l-.017.01-.008.005c-.459.334-.66.698-.657 1.137v.313l-1.25-.003z" />
        </g>
    </svg>
);
export default SvgTooltip;
