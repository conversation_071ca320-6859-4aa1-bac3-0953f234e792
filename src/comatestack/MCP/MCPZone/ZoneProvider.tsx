import constate from 'constate';
import {useDerivedState, useRequestCallback} from 'huse';
import {ReactNode, useCallback, useEffect, useMemo, useRef} from 'react';
import {apiGetMCPZoneDetail, apiGetZoneScenes} from '@/api/mcp';
import {ALL_LABELS, OTHER_LABELS} from '@/components/MCP/MCPServerFilter/LabelsFilterContent';
import {SpaceLabel} from '@/types/mcp/mcp';
import {FetchMCPServersProvider, useFetchMCPServersContext} from '@/components/MCP/MCPServerCard/FetchMCPServersProvider';
import {useMCPZoneIdFromUrl} from './hooks/useMCPZoneIdFromUrl';
import {useZoneProperties} from './hooks/useZoneProperties';
import {initialSearchParams} from './constant';

export const [ZoneProvider, useZoneContext] = constate(() => {
    const {zoneId: parentZoneId, childZoneId, updateChildZone} = useMCPZoneIdFromUrl();
    const [selectedZoneId, setSelectedZone] = useDerivedState(childZoneId ? childZoneId : parentZoneId);
    // 专区及该专区下的子专区的详情是一次性获取的
    const [fetchZoneDetail, {data: zoneDetail}] = useRequestCallback(apiGetMCPZoneDetail, parentZoneId);
    const zoneTabItems = useZoneProperties(zoneDetail);
    // 专区对应的场景需要根据当前专区或子专区动态获取
    const fetchZoneScenes = useCallback(
        () => {
            return apiGetZoneScenes({zoneId: selectedZoneId}).then(res => {
                return [
                    {labelValue: '全部', id: ALL_LABELS, labelType: 'GLOBAL'},
                    ...res,
                    {labelValue: '其他', id: OTHER_LABELS, labelType: 'GLOBAL'},
                ];
            }).catch(error => {
                window.console.error(error);
                return [
                    {labelValue: '全部', id: ALL_LABELS, labelType: 'GLOBAL'},
                    {labelValue: '其他', id: OTHER_LABELS, labelType: 'GLOBAL'},
                ];
            }) as Promise<SpaceLabel[]>;
        },
        [selectedZoneId]
    );
    const handleZoneSelect = useCallback(
        (zoneId: string) => {
            setSelectedZone(zoneId);
            updateChildZone(zoneId);
        },
        [setSelectedZone, updateChildZone]
    );
    const selectedZone = useMemo(
        () => {
            if (zoneTabItems) {
                return zoneTabItems.find(item => item.id === selectedZoneId);
            }
        },
        [zoneTabItems, selectedZoneId]
    );

    useEffect(
        () => {
            if (parentZoneId) {
                fetchZoneDetail();
            }
        },
        [parentZoneId, fetchZoneDetail]
    );

    return {
        zoneDetail,
        fetchZoneScenes,
        zoneTabItems,
        setSelectedZone: handleZoneSelect,
        selectedZone,
        parentZoneId,
    };
});

interface ChildrenProps {
    children: ReactNode;
}

const FetchProvider = ({children}: ChildrenProps) => {
    const {fetchZoneScenes, selectedZone} = useZoneContext();
    const initialZoneId = useRef(selectedZone?.id);

    const stableInitParams = useMemo(
        () => {
            return {
                ...initialSearchParams,
                zoneId: initialZoneId.current,
            };
        },
        []
    );

    return (
        <FetchMCPServersProvider initParams={stableInitParams} fetchScenes={fetchZoneScenes}>
            <ZoneChangeHandler selectedZoneId={selectedZone?.id}>
                {children}
            </ZoneChangeHandler>
        </FetchMCPServersProvider>
    );
};

const ZoneChangeHandler = ({children, selectedZoneId}: {children: ReactNode, selectedZoneId?: string}) => {
    const {changeSearchParams} = useFetchMCPServersContext();
    const prevZoneIdRef = useRef<string | undefined>(selectedZoneId);

    useEffect(
        () => {
            if (prevZoneIdRef.current !== selectedZoneId && selectedZoneId && prevZoneIdRef.current !== undefined) {
                changeSearchParams({zoneId: selectedZoneId});
            }
            prevZoneIdRef.current = selectedZoneId;
        },
        [selectedZoneId, changeSearchParams]
    );

    return <>{children}</>;
};

export const ZoneProviderMixed = ({children}: ChildrenProps) => {
    return (
        <ZoneProvider>
            <FetchProvider>
                {children}
            </FetchProvider>
        </ZoneProvider>
    );
};
