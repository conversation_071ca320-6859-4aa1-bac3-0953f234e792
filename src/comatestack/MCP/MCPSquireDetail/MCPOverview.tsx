import styled from '@emotion/styled';
import {Flex, Tooltip, Typography} from 'antd';
import {useCallback, useMemo, useState} from 'react';
import {Button, message} from '@panda-design/components';
import {Markdown} from '@/design/Markdown';
import {useMCPServerId} from '@/components/MCP/hooks';
import {useMCPServer, loadMCPServer, loadMCPServerTools} from '@/regions/mcp/mcpServer';
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '@/components/MCP/JSONViewer';
import {MCPTrackActions} from '@/api/mcp/track';
import {apiPutRefreshTools} from '@/api/mcp';

const Container = styled(Flex)`
    flex: 1;
    display: grid;
    grid-template-columns: 6fr 4fr;
    gap: 16px;
`;

const Content = styled.div`
    border-radius: 6px;
    border: 1px solid #E8E8E8;
    padding: 16px 24px;
    flex: 1;
    overflow: auto;
    ::-webkit-scrollbar {
        display: none;
    }
`;


const MCPOverview = () => {

    const mcpServerId = useMCPServerId();
    const mcpServer = useMCPServer(mcpServerId);
    const [refreshLoading, setRefreshLoading] = useState(false);

    const config = useMemo(
        () => {
            if (mcpServer?.serverConf?.serverConfig) {
                try {
                    return JSON.parse(mcpServer?.serverConf?.serverConfig);
                } catch (e) {
                    return mcpServer?.serverConf?.serverConfig;
                }
                // return JSON.parse(mcpServer?.serverConf?.serverConfig);
            }
        },
        [mcpServer?.serverConf?.serverConfig]
    );
    const onCopy = useCallback(
        () => {
            MCPTrackActions.copyServerConfig([mcpServerId]);
        },
        [mcpServerId]
    );

    const handleRefreshTools = useCallback(
        async () => {
            if (!mcpServerId) {
                return;
            }

            setRefreshLoading(true);
            try {
                await apiPutRefreshTools({serverId: mcpServerId});
                await loadMCPServer({mcpServerId});
                await loadMCPServerTools({mcpServerId});
                message.success('刷新工具列表成功');
            } catch (error) {
                message.error('刷新工具列表失败');
            } finally {
                setRefreshLoading(false);
            }
        },
        [mcpServerId]
    );
    return (
        <Container>
            <Content>
                <Markdown content={mcpServer?.serverConf?.overview || ''} codeHighlight />
            </Content>
            <Content>
                <Flex vertical gap={12}>
                    <Flex justify="space-between" align="center">
                        <Typography.Title level={4}>Server Config</Typography.Title>
                        {mcpServer?.serverSourceType === 'external' && (
                            <Tooltip title="重新获取工具列表">
                                <Button
                                    type="link"
                                    loading={refreshLoading}
                                    onClick={handleRefreshTools}
                                >
                                    刷新
                                </Button>
                            </Tooltip>
                        )}
                    </Flex>
                    <JSONViewer json={config} onCopy={onCopy} />
                </Flex>
            </Content>
        </Container>
    );
};

export default MCPOverview;
