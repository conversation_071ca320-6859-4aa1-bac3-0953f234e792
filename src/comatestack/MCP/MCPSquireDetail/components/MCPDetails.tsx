import {Flex} from 'antd';
import {memo} from 'react';
import DescriptionItem from '@/design/MCP/MCPDescriptionItem';
import TagGroup from '@/components/MCP/TagGroup';
import {UserAvatarList} from '@/components/MCP/UserAvatarList';
import {MCPServerBase} from '@/types/mcp/mcp';

interface MCPDetailsProps {
    server?: MCPServerBase;
}

const MCPDetails = memo(({server}: MCPDetailsProps) => {
    const hasLabels = server?.labels && server.labels.length > 0;

    return (
        <Flex align="center" gap={40} style={{marginTop: '4px'}}>
            {hasLabels && (
                <DescriptionItem label="场景" labelStyle={{color: '#8F8F8F'}}>
                    <TagGroup
                        labels={server.labels.map(
                            label => ({id: label.id, label: label.labelValue})
                        )}
                        color="light-purple"
                        maxNum={4}
                    />
                </DescriptionItem>
            )}
            <DescriptionItem label="部门" labelStyle={{color: '#8F8F8F'}}>
                {server?.departmentName || '暂无部门信息'}
            </DescriptionItem>
            <DescriptionItem label="联系人" labelStyle={{color: '#8F8F8F'}}>
                <UserAvatarList users={server?.contacts ?? []} max={3} />
            </DescriptionItem>
        </Flex>
    );
});

MCPDetails.displayName = 'MCPDetails';

export default MCPDetails;
