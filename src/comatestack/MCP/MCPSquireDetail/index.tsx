import styled from '@emotion/styled';
import {Flex} from 'antd';
import {createContext, useCallback, useContext, useEffect, useMemo} from 'react';
import {useSearchParams, useSearchParamsUpdate} from '@panda-design/router';
import {But<PERSON>} from '@panda-design/components';
import {loadMCPServer, useMCPServer, loadMCPServerTools, useMCPServerTools} from '@/regions/mcp/mcpServer';
import {useMCPServerId} from '@/components/MCP/hooks';
import bg from '@/assets/mcp/pageBg.png';
import vipbg from '@/assets/mcp/pageVipBg.png';
import TitleTabs from '@/components/MCP/TitleTabs';
import {MCPPlaygroundLink} from '@/links/mcp';
import {MCPServerConfigButton} from '@/components/MCP/MCPToolDebug/MCPServerConfigButton';
import {MCPGlobalVarsProvider} from '@/components/MCP/MCPToolDebug/Providers/MCPServerConfigProvider';
import {useMCPServerConfigModalControl} from '@/components/MCP/MCPToolDebug/hooks/useMCPServerConfigModalControl';
import MCPInfo from './MCPInfo';
import MCPOverview from './MCPOverview';
import MCPTool from './MCPTool';

const MCPSquireDetailContext = createContext({openServerConfigModal: () => { }});
export const useMCPSquireDetailContext = () => useContext(MCPSquireDetailContext);

const Container = styled(Flex) <{official?: boolean}>`
    width: 100%;
    height: calc(100vh - 48px);
    padding: 24px;
    background: url(${({official}) => (official ? vipbg : bg)}) no-repeat;
`;

const MCPSquireDetail = () => {
    const {tab} = useSearchParams();
    const updateSearchParams = useSearchParamsUpdate();
    const mcpServerId = useMCPServerId();
    const mcpServer = useMCPServer(mcpServerId);
    const tools = useMCPServerTools(mcpServerId);
    const {
        visible,
        openModal,
        closeModal,
    } = useMCPServerConfigModalControl();
    const activeKey = tab === 'tools' ? 'tools' : 'overview';

    const options = useMemo(
        () => [
            {label: '概览', key: 'overview'},
            {label: `工具  ${tools?.length || 0}`, key: 'tools'},
        ],
        [tools?.length]
    );
    const showServerConfigButton = useMemo(
        () => {
            const protocol = mcpServer?.serverProtocolType;
            const serverParams = mcpServer?.serverParams;
            return (protocol === 'SSE' || protocol === 'Streamable_HTTP')
                && activeKey === 'tools' && serverParams && serverParams.length > 0;
        },
        [mcpServer, activeKey]
    );
    const handleChange = useCallback(
        (activeKey: string) => {
            updateSearchParams({tab: activeKey});
        },
        [updateSearchParams]
    );
    useEffect(
        () => {
            loadMCPServer({mcpServerId});
            if (mcpServerId) {
                loadMCPServerTools({mcpServerId});
            }
        },
        [mcpServerId]
    );

    return (
        <MCPSquireDetailContext.Provider value={{openServerConfigModal: openModal}}>
            <Container vertical gap={16} official={mcpServer?.officialLabel?.official}>
                <MCPGlobalVarsProvider mcpId={mcpServerId} temporary>
                    <MCPInfo />
                    <TitleTabs
                        items={options}
                        onChange={handleChange}
                        activeKey={activeKey}
                        tabBarExtraContent={
                            <Flex align="center" gap={4}>
                                <MCPPlaygroundLink
                                    blank
                                    // eslint-disable-next-line max-len
                                    serverId={(mcpServer?.serverProtocolType === 'SSE' || mcpServer?.serverProtocolType === 'Streamable_HTTP') ? mcpServer?.id : undefined}
                                >
                                    <Button type="link">✨ 去MCP Playground试用 ✨</Button>
                                </MCPPlaygroundLink>
                                {showServerConfigButton && (
                                    <MCPServerConfigButton
                                        modalVisible={visible}
                                        closeModal={closeModal}
                                        openModal={openModal}
                                    />
                                )}
                            </Flex>
                        }
                    />
                    {activeKey === 'overview' ? <MCPOverview /> : <MCPTool />}
                </MCPGlobalVarsProvider>
            </Container>
        </MCPSquireDetailContext.Provider>
    );
};

export default MCPSquireDetail;
