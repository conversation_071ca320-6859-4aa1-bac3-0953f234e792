/* eslint-disable max-lines */
import {Divider, Empty, Flex, Form, Input, Space, Typography} from 'antd';
import {useCallback, useEffect, useRef} from 'react';
import styled from '@emotion/styled';
import {useMCPServerId} from '@/components/MCP/hooks';
import {BaseParam} from '@/types/mcp/mcp';
import {loadMCPServerToolItem, useMCPServer, useMCPServerToolItem} from '@/regions/mcp/mcpServer';
import AffixPanel from '@/components/MCP/AffixPanel';
import {MCPToolParamsForm} from '@/components/MCP/MCPToolDebug/MCPToolParamsForm';
import {MCPToolDebugButton} from '@/components/MCP/MCPToolDebug/MCPToolDebugButton';
import {MCPToolDebugResult} from '@/components/MCP/MCPToolDebug/MCPToolDebugResult';
import {MCPToolContext} from '@/components/MCP/MCPToolDebug/Providers/MCPToolContext';
import {useMCPToolDebugContext} from '@/components/MCP/MCPToolDebug/Providers/MCPToolDebugProvider';
import {useMCPSquireDetailContext} from './index';

const Wrapper = styled(Form)`
    .ant-5-form-item {
        margin-bottom: 16px;
    }
    .ant-5-form-item-row {

        .ant-5-form-item-label {
            label {
                width: 150px!important;
                word-break: break-all;
            }
        }
    }
`;

const ResultContainer = styled.div`
    position: relative;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    flex-shrink: 0;
    min-height: 200px;
    .mcp-tool-debug-result-container{
        .json-viewer-container{
            height: 1px;
            flex-grow: 1;
        }
    }
`;

const DebugResultContainer = styled.div`
    min-height: 200px;
    margin-top: 16px;
    flex-grow: 1;
`;

const caculatePlaceholder = (item: BaseParam) => {
    return `${item.description || '-'}，类型：${item.dataType || '-'}，示例：${item.exampleValue || '-'}`;
};

function DebugButtonAndForm() {
    const {openServerConfigModal} = useMCPSquireDetailContext();
    const ref = useRef<HTMLDivElement|null>(null);
    const {runDebug} = useMCPToolDebugContext();
    const handlerDebug = useCallback(
        async () => {
            const res = await runDebug();
            if (res.type === 'success' && ref.current) {
                const labelDom = ref.current.querySelector(`label[for=${res}]`);
                if (labelDom) {
                    labelDom.scrollIntoView({
                        behavior: 'smooth',
                        block: 'center',
                        inline: 'center',
                    });
                }
            } else if (res.type === 'server_params_missing') {
                openServerConfigModal();
            }
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [runDebug, openServerConfigModal]
    );
    return (
        <div style={{flex: '0 0 auto'}}>
            <div style={{paddingRight: '16px'}} ref={ref}><MCPToolParamsForm /></div>
            <MCPToolDebugButton topHandler={handlerDebug} />
        </div>
    );
}

function DebugResult() {
    // const {toolParams} = useMCPToolDebugContext();
    // const height = useMemo(
    //     () => {
    //         if(!containerHeight) {

    //         }
    //         if (toolParams.length > 0) {
    //             return '200px';
    //         } else {
    //             return '300px';
    //         }
    //     },
    //     [toolParams, containerHeight]
    // );
    return (
        <ResultContainer>
            <Typography.Title level={4}>结果</Typography.Title>
            <DebugResultContainer>
                <MCPToolDebugResult card />
            </DebugResultContainer>
        </ResultContainer>
    );
}

interface Props {
    toolId: number;
}

export default function MCPToolDetail({toolId}: Props) {
    const containerRef = useRef<HTMLDivElement|null>(null);
    const [form] = Form.useForm();
    const mcpServerId = useMCPServerId();
    const toolDetail = useMCPServerToolItem(toolId);
    const toolKey = toolDetail?.toolKey;
    const mcpServer = useMCPServer(mcpServerId);
    useEffect(
        () => {
            if (!mcpServerId || !toolId) {
                return;
            }
            loadMCPServerToolItem({mcpServerId, toolId});
        },
        [mcpServerId, toolId]
    );

    useEffect(
        () => {
            if (toolDetail) {
                form.setFieldsValue({
                    toolParams: toolDetail.toolParams,
                });
            }
        },
        [toolDetail, form]
    );

    // const containerHeight = containerRef.current?.offsetHeight || 0;
    return (
        <AffixPanel
            header={(
                <Space size={16}>
                    <Typography.Title level={4}>
                        {mcpServer?.serverProtocolType === 'STDIO' ? '工具详情' : '工具试用'}
                    </Typography.Title>
                    <span style={{color: '#3C3C3C'}}>工具标识：{toolDetail?.toolKey}</span>
                </Space>
            )}
            style={{height: '100%', overflow: 'visible'}}
        >

            {
                mcpServer?.serverProtocolType === 'STDIO'
                    ? (
                        <Wrapper form={form} labelAlign="left" colon={false}>
                            <Form.Item label="工具">
                                <span>{toolDetail?.name}</span>
                            </Form.Item>
                            <Form.Item label="工具描述">
                                <div>{toolDetail?.description}</div>
                            </Form.Item>
                            {!toolDetail?.toolParams?.toolParams?.length && (
                                <Empty description="工具不需要输入参数就能执行" image={Empty.PRESENTED_IMAGE_SIMPLE} />
                            )}
                            {toolDetail?.toolParams?.toolParams?.map((item, index) => (
                                <Form.Item
                                    key={`toolParams_${index}`}
                                    label={(<Typography.Text type="secondary" ellipsis>{item.name}</Typography.Text>)}
                                    name={['toolParams', 'toolParams', index, 'defaultValue']}
                                    required={item.required}
                                >
                                    <Input
                                        disabled
                                        placeholder={caculatePlaceholder(item)}
                                    />
                                </Form.Item>
                            ))}
                        </Wrapper>
                    ) : (
                        <MCPToolContext
                            mcpId={mcpServerId}
                            toolId={toolId}
                            toolKey={toolKey}
                            inPlayground={false}
                            ignoreGlobalVarsProvider
                        >
                            <Flex vertical flex="1" ref={containerRef}>
                                <DebugButtonAndForm />
                                <Divider />
                                <DebugResult />
                            </Flex>
                        </MCPToolContext>
                    )
            }
        </AffixPanel>
    );
}
