import {Table, Tooltip} from 'antd';
import {ColumnsType} from 'antd/es/table';
import {MCPServerAnalysisData} from '@/types/mcp/mcp';
import {IconTooltip} from '@/icons/mcp/index';

interface ToolData {
    id: number;
    toolName: string;
    callCount: number;
    callSuccessRate: string;
}

interface Props {
    data: MCPServerAnalysisData | null;
}

const ToolDetailTable = ({data}: Props) => {
    const columns: ColumnsType<ToolData> = [
        {
            title: '工具名称',
            dataIndex: 'toolName',
            key: 'toolName',
        },
        {
            title: (
                <span style={{display: 'flex', alignItems: 'center'}}>
                    调用数量
                    <Tooltip title="MCP内各个工具被调用的次数。">
                        <IconTooltip style={{marginLeft: '4px', cursor: 'pointer'}} />
                    </Tooltip>
                </span>
            ),
            dataIndex: 'callCount',
            key: 'callCount',
        },
        {
            title: (
                <span style={{display: 'flex', alignItems: 'center'}}>
                    调用成功率
                    <Tooltip title="成功工具调用次数占总调用次数的百分比。">
                        <IconTooltip style={{marginLeft: '4px', cursor: 'pointer'}} />
                    </Tooltip>
                </span>
            ),
            dataIndex: 'callSuccessRate',
            key: 'callSuccessRate',
            render: (rate: string) => `${rate}%`,
        },
        // {
        //     title: '调用来源',
        //     key: 'source',
        //     render: () => '详情',
        // },
    ];

    return (
        <Table
            columns={columns}
            dataSource={data?.toolDataList || []}
            rowKey="id"
            pagination={false}
        />
    );
};

export default ToolDetailTable;
