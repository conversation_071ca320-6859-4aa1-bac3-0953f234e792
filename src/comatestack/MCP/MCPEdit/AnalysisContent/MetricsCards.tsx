import {Flex, Tooltip} from 'antd';
import styled from '@emotion/styled';
import {MCPServerAnalysisData} from '@/types/mcp/mcp';
import bg from '@/assets/mcp/mcpServerCoreDataBg.png';
import {IconTooltip} from '@/icons/mcp/index';

const MetricsCardContainer = styled(Flex)`
    gap: 12px;
`;

const MetricCard = styled.div`
    flex: 1;
    border-radius: 8px;
    padding: 16px;
    background-image: url(${bg});
    border: 1px solid var(--Tokens-, #E8E8E8)
`;

const MetricLabel = styled.div`
    color: #545454;
    font-size: 14px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
`;

const MetricValue = styled.div`
    font-size: 24px;
    font-weight: bold;
    color: #000;
`;

interface Props {
    data: MCPServerAnalysisData | null;
}

const getComparisonText = (currentValue: number, averageValue: number): string => {
    const ratio = currentValue / averageValue;
    if (ratio > 1.2) {
        return '高于';
    } else if (ratio < 0.8) {
        return '低于';
    } else {
        return '接近';
    }
};

const getSuccessRateComparisonText = (currentRate: string, averageRate: number): string => {
    const current = parseFloat(currentRate);
    if (isNaN(current)) {
        return '接近';
    }

    if (current > averageRate * 1.1) {
        return '优于';
    } else if (current < averageRate * 0.9) {
        return '低于';
    } else {
        return '接近';
    }
};

const MetricsCards = ({data}: Props) => {
    const platformAverageCallCount = 1000;
    const platformAverageSuccessRate = 85.0;

    const currentCallCount = data?.callCount || 0;
    const currentSuccessRate = data?.callSuccessRate || '0.0';

    const callCountComparison = getComparisonText(currentCallCount, platformAverageCallCount);
    const successRateComparison = getSuccessRateComparisonText(currentSuccessRate, platformAverageSuccessRate);

    const callCountTooltip = `通过平台网关调用此MCP内工具的总次数（当前使用率：${currentCallCount}，${callCountComparison}平台平均水平）。`;
    const successRateTooltip = '本MCP工具成功被调用次数占总被调用次数的百分比'
        + `（当前成功率：${currentSuccessRate}%，${successRateComparison}MCP调用平均水平）。`;

    return (
        <MetricsCardContainer>
            <MetricCard>
                <MetricLabel>
                    浏览量（PV/UV）
                    <Tooltip title="展示该MCP在广场和空间内的访问数据：总访问次数（PV）和独立访客数（UV）。">
                        <IconTooltip style={{marginLeft: '4px', cursor: 'pointer'}} />
                    </Tooltip>
                </MetricLabel>
                <MetricValue>{data?.pv || 0}/{data?.uv || 0}</MetricValue>
            </MetricCard>
            <MetricCard>
                <MetricLabel>
                    总调用量
                    <Tooltip title={callCountTooltip}>
                        <IconTooltip style={{marginLeft: '4px', cursor: 'pointer'}} />
                    </Tooltip>
                </MetricLabel>
                <MetricValue>{data?.callCount || 0}</MetricValue>
            </MetricCard>
            <MetricCard>
                <MetricLabel>
                    订阅应用量
                    <Tooltip title="当前订阅此MCP Server的独立应用总数。">
                        <IconTooltip style={{marginLeft: '4px', cursor: 'pointer'}} />
                    </Tooltip>
                </MetricLabel>
                <MetricValue>{data?.subCount || 0}</MetricValue>
            </MetricCard>
            <MetricCard>
                <MetricLabel>
                    Server Config复制量
                    <Tooltip title="&quot;Server Config&quot;区域的&quot;复制&quot;按钮被点击的总次数。">
                        <IconTooltip style={{marginLeft: '4px', cursor: 'pointer'}} />
                    </Tooltip>
                </MetricLabel>
                <MetricValue>{data?.copyCount || 0}</MetricValue>
            </MetricCard>
            <MetricCard>
                <MetricLabel>
                    总调用成功率
                    <Tooltip title={successRateTooltip}>
                        <IconTooltip style={{marginLeft: '4px', cursor: 'pointer'}} />
                    </Tooltip>
                </MetricLabel>
                <MetricValue>{data?.callSuccessRate}%</MetricValue>
            </MetricCard>
        </MetricsCardContainer>
    );
};

export default MetricsCards;
