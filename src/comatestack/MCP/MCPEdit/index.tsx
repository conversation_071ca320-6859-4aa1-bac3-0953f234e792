import {memo, useEffect} from 'react';
import {Form} from 'antd';
import {useSearchParams} from '@panda-design/router';
import {Gap} from '@/design/iplayground/Gap';
import {useMCPServerId} from '@/components/MCP/hooks';
import {loadMCPServer, useMCPServer} from '@/regions/mcp/mcpServer';
import {useSearchReplace} from '@/hooks/icode/common/useSearchParams';
import {MCPEditTab} from '@/types/mcp/mcp';
import ToolsContent from './ToolsContent';
import BasicInfoContent from './BasicInfoContent';
import AnalysisContent from './AnalysisContent';
import {useTouchedBasePath} from './regions';
import LeavePageConfirmModal from './LeavePageConfirmModal';
import {onValueChange} from './hooks';
import {MCPEditFormItemProvider} from './Providers/MCPEditFormItemProvider';
import {MCPEditFormValidationInteractionProvider} from './Providers/MCPEditFormValidationInteractionProvider';
import {useNavigationBlocker} from './hooks/useNavigationBlocker';
import {useFormReset} from './hooks/useFormReset';
import {FormWrapper, Content} from './styles';
import {disabledName} from './constants';
import Header from './Header';
import {FormUpdatedProvider} from './Providers/FormUpdatedProvider';
import {ToolParamsConfigProvider} from './Providers/ToolParamsConfigProvider';

const MCPEdit = memo(() => {
    const {activeTab: activeTabParam} = useSearchParams();
    const replace = useSearchReplace();
    const activeTab = (activeTabParam as MCPEditTab) || MCPEditTab.ServerInfo;
    const setActiveTab = (tab: string) => replace({activeTab: tab});

    const mcpServerId = useMCPServerId();
    const mcpServer = useMCPServer(mcpServerId);
    const touchedBasePath = useTouchedBasePath();

    const {blocker, nextLocation, leaveConfirmType, handleConfirmLeave, handleCancelLeave, form} =
        useNavigationBlocker({activeTab, touchedBasePath});

    // 这里只是处理basicInfo的数据。tool的数据在ToolsContent中处理。
    const resetForm = useFormReset({form, mcpServer});

    useEffect(
        () => {
            loadMCPServer({mcpServerId});
        },
        [mcpServerId]
    );

    useEffect(
        () => {
            if (mcpServer) {
                // 用这个方法来初始化表单数据
                resetForm();
            }
        },
        [resetForm, mcpServer]
    );

    return (
        <MCPEditFormValidationInteractionProvider>
            <MCPEditFormItemProvider
                serverSourceType={mcpServer?.serverSourceType}
                activeTab={activeTab}
                disableExclude={disabledName}
            >
                <FormUpdatedProvider>
                    <FormWrapper
                        official={mcpServer?.officialLabel?.official}
                        form={form}
                        colon={false}
                        labelCol={{flex: '100px'}}
                        labelAlign="left"
                        onFieldsChange={
                            field => onValueChange(field, form)
                        }
                    >
                        <ToolParamsConfigProvider form={form}>
                            <Header
                                activeKey={activeTab}
                                onChange={setActiveTab}
                            />
                            <Gap />
                            <Content border={activeTab === MCPEditTab.ServerInfo}>
                                {activeTab === MCPEditTab.ServerInfo && <BasicInfoContent />}
                                {activeTab === MCPEditTab.Tools && <ToolsContent />}
                                {activeTab === MCPEditTab.Analysis && <AnalysisContent />}
                            </Content>
                            <Form.Item name="serverSourceType" hidden />
                            <Form.Item name="tools" hidden />
                            {nextLocation && blocker.state === 'blocked' && (
                                <LeavePageConfirmModal
                                    nextLocation={nextLocation}
                                    onCancel={handleCancelLeave}
                                    type={leaveConfirmType}
                                    resetForm={resetForm}
                                    onConfirmLeave={handleConfirmLeave}
                                />
                            )}
                        </ToolParamsConfigProvider>
                    </FormWrapper>
                </FormUpdatedProvider>
            </MCPEditFormItemProvider>
        </MCPEditFormValidationInteractionProvider>
    );
});

export default MCPEdit;
