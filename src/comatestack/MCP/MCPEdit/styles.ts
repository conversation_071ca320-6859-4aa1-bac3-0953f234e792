import styled from '@emotion/styled';
import {css} from '@emotion/css';
import {Form} from 'antd';
import bg from '@/assets/mcp/pageBg.png';
import vipbg from '@/assets/mcp/pageVipBg.png';

/**
 * calc尽量少用，特别是计算量中包含绝对数值的时候。如果布局有改动，calc中使用的相关值很有可能要跟着改，比较费劲。
 * 最好使用flex做成跟着布局自动拉宽或拉高的。
 */
export const FormWrapper = styled(Form)<{official?: boolean}>`
    height: calc(100vh - 48px);
    width: 100%;
    display: flex;
    flex-direction: column;
    background: url(${({official}) => (official ? vipbg : bg)}) no-repeat;
`;

export const Content = styled.div<{border: boolean}>`
    padding: 16px 0;
    height: calc(100vh - 200px);
    display: flex;
    flex-direction: column;
    border-radius: 6px;
    flex-grow: 1;
    border: ${({border}) => (border ? '1px solid #E8E8E8' : 'none')};
    margin: 0 24px 24px !important;
`;

export const errorRowCss = css`
    td {
        &: first-child {
            border-left: 2px solid #e62c4b !important;
        }
    }
`;
