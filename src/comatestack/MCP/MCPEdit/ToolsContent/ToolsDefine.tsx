/* eslint-disable max-lines */
import styled from '@emotion/styled';
import {Collapse, Flex, Form, Space, Tooltip} from 'antd';
import {ReactNode, useMemo} from 'react';
import {QuestionCircleOutlined} from '@ant-design/icons';
import {useMCPEditFormItem} from '../Providers/MCPEditFormItemProvider';
import ResponseTemplate from './Response/ResponseTemplate';
import ToolInfo from './ToolInfo';
import ParamsListOfToolAdd from './ToolAddModal/ParamsListOfToolAdd';
import BashField from './BashField';
import ParamsList, {RequiredTitle} from './ParamList';
import {useActiveTool} from './hooks';

export const StyledTitle = styled.h3`
    font-size: 16px;
    line-height: 24px;
    font-weight: 500;
    margin: 16px 0;
    color: #000;
`;

const Line = styled.div`
    height: 12px;
    width: 1px;
    background: #D9D9D9;
`;

const GrayText = styled.span`
    color: #8F8F8F;
    font-size: 12px;
    font-weight: 400;
`;

export const StyledCollapse = styled(Collapse)`
    background-color: #fff !important;
    .ant-5-collapse-content {
        padding-top: 16px !important;
    }
    .ant-5-collapse-item{
        border: none !important;
        margin-bottom: 12px !important;
        .ant-5-collapse-expand-icon > svg {
            transform: rotate(-90deg) !important;
        }
        }
    .ant-5-collapse-item-active{
        .ant-5-collapse-expand-icon > svg {
            transform: rotate(-90deg) !important;
        }
    }
    .ant-5-collapse-header{
        background: linear-gradient(270deg, rgba(245, 247, 250, 0.296972) 0%, #F5F7FA 100%) !important;
        border-radius: 6px !important;
        position: relative !important;
        padding: 5px 12px !important;
        font-weight: 600 !important;
        .ant-5-collapse-expand-icon{
            color: #8F8F8F !important;
            position: absolute !important;
            right: 10px !important;
            top: 50% !important;
            transform: translateY(-50%) !important;
        }
    }
`;

const Content = styled.div`
    height: 0px;
    flex-grow: 1;
    overflow-y: auto;
`;

interface ItemType {
    key: string;
    children: ReactNode;
    label: ReactNode;
}

interface Props {
    showApiDefine: boolean;
}

const ToolsDefine = ({showApiDefine}: Props) => {
    const {MCPEditFormItem} = useMCPEditFormItem();
    const serverSourceType = Form.useWatch('serverSourceType');
    const {activeToolIndex} = useActiveTool();
    const responseTemplate: string | null = Form.useWatch(['tools', activeToolIndex, 'toolConf', 'responseTemplate']);
    const basePath = useMemo(
        () => ['tools', activeToolIndex],
        [activeToolIndex]
    );
    const itemsServerTypeMap = useMemo<Record<string, ItemType[]>>(
        () => ({
            openapi: [
                {
                    key: 'paramsList',
                    children: <ParamsList path={[...basePath, 'toolParams', 'toolParams']} />,
                    label: (
                        <Space size={12}>
                            <span>参数</span>
                            <Line />
                            <GrayText>添加需要大模型输入的参数，API请求的其他必填参数需要有默认值</GrayText>
                        </Space>
                    ),
                },
                {
                    key: 'responseTemplate',
                    children: <ResponseTemplate template={responseTemplate} />,
                    label: (
                        <Flex gap="small" align="center">
                            <span>响应模板</span>
                            <Tooltip title="实际调用时会返回API的真实响应">
                                <QuestionCircleOutlined />
                            </Tooltip>
                            <Line />
                            <GrayText>
                                从接口响应中取出需要的信息，组合成为适合大模型理解的文本；
                                <br />
                                若不进行配置，MCP Client需解析响应JSON后再自己组合
                            </GrayText>
                        </Flex>
                    ),
                },
            ],
            script: [
                {
                    key: 'paramsList',
                    children: (
                        <Form.Item name={[...basePath, 'toolParams', 'toolParams']}>
                            <ParamsListOfToolAdd path={[...basePath, 'toolParams', 'toolParams']} />
                        </Form.Item>
                    ),
                    label: (
                        <Space size={12}>
                            <span>参数</span>
                            <Line />
                            <GrayText>添加需要大模型输入的参数</GrayText>
                        </Space>
                    ),
                },
                {
                    key: 'bashType',
                    children: <BashField path={[...basePath, 'toolConf']} />,
                    label: <RequiredTitle>脚本类型</RequiredTitle>,
                },
            ],
            external: [
                {
                    key: 'paramsList',
                    children: (
                        <MCPEditFormItem name={[...basePath, 'toolParams', 'toolParams']}>
                            <ParamsListOfToolAdd path={[...basePath, 'toolParams', 'toolParams']} />
                        </MCPEditFormItem>
                    ),
                    label: '参数',
                },
            ],
        }),
        [basePath, responseTemplate, MCPEditFormItem]
    );
    const items = useMemo(
        () => [
            {
                key: 'define',
                children: <ToolInfo path={basePath} />,
                label: '工具信息',
            },
            ...itemsServerTypeMap[serverSourceType] ?? [],
        ],
        [basePath, itemsServerTypeMap, serverSourceType]
    );

    return (
        <Flex
            style={{
                width: showApiDefine && serverSourceType !== 'script' ? '50%' : '1016px',
            }}
            vertical
        >
            <StyledTitle>工具定义</StyledTitle>
            <Content>
                <StyledCollapse
                    defaultActiveKey={['define', 'paramsList', 'responseTemplate', 'bashType']}
                    items={items}
                    bordered={false}
                />
            </Content>
        </Flex>
    );
};

export default ToolsDefine;
