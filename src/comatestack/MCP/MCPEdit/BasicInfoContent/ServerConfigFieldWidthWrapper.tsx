import {Form, Tooltip, Flex} from 'antd';
import {Path} from '@panda-design/path-form';
import {useCallback, useState} from 'react';
import {Button, message} from '@panda-design/components';
import {useMCPServerId} from '@/components/MCP/hooks';
import {apiPutRefreshTools} from '@/api/mcp';
import {loadMCPServer, loadMCPServerTools} from '@/regions/mcp/mcpServer';
import ServerConfigField from './ServerConfigField';
import {ServerConfigHelpDoc} from './ServerConfigHelpDoc';

interface Props {
    path?: Path;
}
const ServerConfigFieldWidthWrapper = ({path}: Props) => {
    const serverProtocolType = Form.useWatch([...path, 'serverProtocolType']);
    const mcpServerId = useMCPServerId();
    const [refreshLoading, setRefreshLoading] = useState(false);

    const handleRefreshTools = useCallback(
        async () => {
            if (!mcpServerId) {
                return;
            }

            setRefreshLoading(true);
            try {
                await apiPutRefreshTools({serverId: mcpServerId});
                await loadMCPServer({mcpServerId});
                await loadMCPServerTools({mcpServerId});
                message.success('刷新工具列表成功');
            } catch (error) {
                message.error('刷新工具列表失败');
            } finally {
                setRefreshLoading(false);
            }
        },
        [mcpServerId]
    );

    return (
        <>
            <Flex justify="space-between" align="center" style={{marginBottom: 8}}>
                <span>
                    <span style={{color: '#ff4d4f', marginRight: 4, fontFamily: 'SimSun, sans-serif'}}>*</span>
                    服务器配置
                    <ServerConfigHelpDoc serverProtocolType={serverProtocolType} />
                </span>
                <Tooltip title="重新获取工具列表">
                    <Button
                        type="link"
                        loading={refreshLoading}
                        onClick={handleRefreshTools}
                    >
                        刷新
                    </Button>
                </Tooltip>
            </Flex>
            <ServerConfigField showLabel={false} path={path} />
        </>
    );
};

export default ServerConfigFieldWidthWrapper;

