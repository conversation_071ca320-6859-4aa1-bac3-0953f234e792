import {sortBy, groupBy, flatten, partition, cloneDeep, remove, concat, isEmpty, compact} from 'lodash';
import {SCORE_WEIGHT_ORDER, INTELLIGENT_CODE_FLAG, SUGGESTION_CODE_FLAG} from '@/constants/icode/review';
import {
    ChangeInfo,
    PatchSet,
    Job,
    ChangeInfoPipeline,
} from '@/types/icode/review';
import {
    Comment,
    CommentRoot,
    CodeComment,
    CommentRange,
    GerritCommentRange,
    CommentType,
} from '@/types/icode/comment';
import {toDate} from '@/utils/icode/date';
import {QUICK_REPLY_COMMENT_TYPE} from '@/constants/icode/review';

interface Reviewer {
    value?: number;
}

// 计算评审的最终得分
export const computeReviewScore = (reviewers: Reviewer[]): number => {
    const scores = reviewers.map(item => item?.value);
    for (const current of SCORE_WEIGHT_ORDER) {
        if (scores.includes(current)) {
            return current;
        }
    }
    return 0;
};

// replyTo === null 为起点，构造多棵树，然后前序遍历（根左右）
// istanbul ignore next should fix 为了 100% 暂不处理
export const groupComments = (comments: Comment[]): CommentRoot[] => {
    // NOTE 可以换成 formatDateTime
    // istanbul ignore next should fix 为了 100% 暂不处理
    const sortByTime = ({updated}: Comment) => {
        return updated ? toDate(updated) : new Date();
    };

    const commonComments = cloneDeep(comments);
    const quickReplyComments = remove(commonComments, key => QUICK_REPLY_COMMENT_TYPE.includes(key.commentType ?? ''));
    const groupQuickReplyComments = groupBy(quickReplyComments, 'in_reply_to');
    const sortedComments = sortBy(commonComments, sortByTime);
    // in_reply_to === null 是一级评论，起点
    const [roots, rest] = partition(sortedComments, comment => comment.in_reply_to === null);

    // 根据 in_reply_to 分组，好找
    const group = groupBy(rest, 'in_reply_to');

    // istanbul ignore next should fix 为了 100% 暂不处理
    const traversal = (comment: Comment): Comment[] | undefined => {
        const children = group[comment.id];
        if (!children) {
            // istanbul ignore next should fix 为了 100% 暂不处理
            return;
        }
        // 删掉已经处理过的 comments
        delete group[comment.id];
        const result: Comment[] = [];
        children.forEach(child => {
            if (groupQuickReplyComments[child.id]) {
                child.children = groupQuickReplyComments[child.id];
            }
            // 递归搜索
            result.push(child);
            const nestedChildren = traversal(child);
            nestedChildren && result.push(...nestedChildren);
        });
        return result;
    };

    // istanbul ignore next should fix 为了 100% 暂不处理
    const commentRoots: CommentRoot[] = roots.map(comment => {
        const children = compact(concat(traversal(comment) ?? [], groupQuickReplyComments[comment.id]));
        return {
            ...comment,
            children: isEmpty(children) ? undefined : children,
        };
    });

    // group 应该是空的，否则就是有 invalid 的回复，现在的处理方式是把它们加在最后
    const values = Object.values(group);
    // istanbul ignore next should fix 为了 100% 暂不处理
    return commentRoots.concat(flatten(values));
};

export const parseBaseCommit = (baseCommit: string | null | undefined, commitId?: string) => {
    // commitId === 'BASE' 的时候一定有 baseCommit
    return (commitId === 'BASE' ? baseCommit : commitId);
};

interface Params {
    isDelete: boolean;
    line: number;
    aPatchSet: PatchSet;
    bPatchSet: PatchSet;
    path: string;
    range?: CommentRange;
}

export const getNewComment = ({isDelete, line, aPatchSet, bPatchSet, path, range}: Params): Comment => {
    const commentBaseInfo = {
        id: Math.random().toString().replace(/\./, ''),
        type: 'NEW' as 'NEW',
        line,
        range,
        message: '',
        isEditing: true,
        author: '',
        updated: '',
        path,
    };
    if (isDelete) {
        if (aPatchSet.patchSetId) {
            return {
                ...commentBaseInfo,
                changeId: aPatchSet.changeId ?? 0,
                patchSetId: aPatchSet.patchSetId ?? 0,
            };
        }
        else {
            return {
                ...commentBaseInfo,
                side: 'PARENT',
                changeId: bPatchSet.changeId ?? 0,
                patchSetId: bPatchSet.patchSetId ?? 0,
            };
        }
    }
    else {
        return {
            ...commentBaseInfo,
            changeId: bPatchSet.changeId ?? 0,
            patchSetId: bPatchSet.patchSetId ?? 0,
        };
    }
};

type CrStatus = 'CR_PASS' | 'CR_DENIED' | 'CR_NEED';

// NOTE 稳定后移至 util 并添加测试
export const selectCrStatus = ({changeStatus, crNeed, globalReviewers}: ChangeInfo): CrStatus => {
    if (changeStatus === 'CR_NEED') {
        return 'CR_NEED';
    }
    if (!crNeed) {
        return 'CR_PASS';
    }
    // 需要人工评审且没有明确评审未通过时, 需要通过分数检查人工评审状态
    const humanReviewers = globalReviewers.filter(({robot}) => !robot);
    const codeReviewRejected = humanReviewers.some(({value}) => value === -2);

    if (codeReviewRejected) {
        return 'CR_DENIED';
    }

    const codeReviewAccepted = humanReviewers.some(({value}) => value === 2);

    return codeReviewAccepted ? 'CR_PASS' : 'CR_NEED';
};

export const getRobotQuery = (job: Job) => {
    const {checkTool, checkJob} = job;
    const {taskId} = checkJob;
    const {classify} = checkTool;
    return {taskId: `${taskId}`, toolClassify: classify};
};

export interface ChangeInfoPipelineWithTimeDiff extends ChangeInfoPipeline {
    timeDiff: number;
}

type FormatPipelines = ChangeInfoPipelineWithTimeDiff[] | [{isPipelineExempted: true}];

export const formatPipelines = (changeInfo: ChangeInfo): FormatPipelines => {
    if (!changeInfo) {
        return [];
    }
    // NOTE 把 pipelineInfo 映射到 pipelines
    const {ignoreAgileScore, pipelineInfo, revisionTipCreateTimeInfo} = changeInfo;
    const {changePipelines, exemptInfo, hasPipelines} = pipelineInfo;
    if (ignoreAgileScore) {
        return [];
    }
    if (exemptInfo || !hasPipelines) {
        return [{isPipelineExempted: true}];
    }
    if (changePipelines && changePipelines.length > 0) {
        const {timeDiff} = revisionTipCreateTimeInfo || {};
        // 某个注入，保持原逻辑
        return changePipelines.map(item => ({timeDiff, ...item}));
    }
    return [];
};

// eslint-disable-next-line complexity, max-statements
const analyzeCommentSuggestion = (value: string): CodeComment[] => {
    const formatValue = value.replace(/(\n)?(```|```[\s]+)suggestion/g, `\n${SUGGESTION_CODE_FLAG}`);
    if (!formatValue.includes(SUGGESTION_CODE_FLAG)) {
        return [{type: CommentType.COMMON, message: value, key: Math.random()}];
    }
    let list = formatValue.split(/\r?\n/);
    if (formatValue.startsWith(`\n${SUGGESTION_CODE_FLAG}`)) {
        list = list.slice(1);
    }
    const result: CodeComment[] = [];
    let commonMessage: string[] = [];
    let suggestionMessage: string[] = [];
    let isSuggestion = false;
    let suggestionCount = 0;
    for (const nowValue of list) {
        if (nowValue.startsWith(SUGGESTION_CODE_FLAG)) {
            suggestionMessage = [nowValue];
            isSuggestion = true;
            commonMessage.length && result.push({
                type: CommentType.COMMON,
                message: commonMessage.join('\n'),
                key: Math.random(),
            });
            commonMessage = [];
        }
        else if (nowValue.includes('```') && isSuggestion) {
            const index = nowValue.indexOf('```') + 3;
            suggestionMessage.push(nowValue.slice(0, index));
            const endValue = nowValue.slice(index);
            commonMessage = endValue ? [endValue] : [];
            isSuggestion = false;
            result.push({
                type: CommentType.SUGGESTION,
                message: suggestionMessage.join('\n'),
                key: Math.random(),
                title: `代码建议${suggestionCount + 1}`,
                index: suggestionCount,
            });
            suggestionCount++;
            suggestionMessage = [];
        }
        else if (isSuggestion) {
            suggestionMessage.push(nowValue);
        }
        else {
            commonMessage.push(nowValue);
        }
    }
    if (!isEmpty(commonMessage)) {
        const message = commonMessage.join('\n');
        message && result.push({type: CommentType.COMMON, message, key: Math.random()});
    }
    if (!isEmpty(suggestionMessage)) {
        const endIndex = suggestionMessage.length - 1;
        if (!suggestionMessage[endIndex].endsWith('```')) {
            suggestionMessage[endIndex] = suggestionMessage[endIndex] + '```';
        }
        result.push({
            type: CommentType.SUGGESTION,
            message: suggestionMessage.join('\n'),
            key: Math.random(),
            title: `代码建议${suggestionCount + 1}`,
            index: suggestionCount,
        });
        suggestionCount++;
    }
    return result;
};

// eslint-disable-next-line complexity, max-statements
export const analyzeCommentCode = (value: string): CodeComment[] => {
    const formatValue = value.replace(/(\n)?(```|```[\s]+)intelligentCode/g, `\n${INTELLIGENT_CODE_FLAG}`);
    let intelligentCodeCount = 0;
    if (!formatValue.includes(INTELLIGENT_CODE_FLAG)) {
        return analyzeCommentSuggestion(value);
    }
    let list = formatValue.split(/\r?\n/);
    if (formatValue.startsWith(`\n${INTELLIGENT_CODE_FLAG}`)) {
        list = list.slice(1);
    }
    const result: CodeComment[] = [];
    const finalResult: CodeComment[] = [];
    let commonMessage: string[] = [];
    let intelligentCodeMessage: string[] = [];
    let isIntelligentCode = false;
    for (const nowValue of list) {
        if (nowValue.startsWith(INTELLIGENT_CODE_FLAG)) {
            intelligentCodeMessage = [nowValue];
            isIntelligentCode = true;
            // eslint-disable-next-line max-len
            commonMessage.length && result.push({type: CommentType.COMMON, message: commonMessage.join('\n'), key: Math.random()});
            commonMessage = [];
        }
        else if (nowValue.includes('```') && isIntelligentCode) {
            const index = nowValue.indexOf('```') + 3;
            intelligentCodeMessage.push(nowValue.slice(0, index));
            const endValue = nowValue.slice(index);
            commonMessage = endValue ? [endValue] : [];
            isIntelligentCode = false;
            result.push({
                type: CommentType.INTELLIGENT_CODE,
                message: intelligentCodeMessage.join('\n'),
                key: Math.random(),
                title: `智能评审${intelligentCodeCount + 1}`,
            });
            intelligentCodeCount++;
            intelligentCodeMessage = [];
        }
        else if (isIntelligentCode) {
            intelligentCodeMessage.push(nowValue);
        }
        else {
            commonMessage.push(nowValue);
        }
    }
    if (!isEmpty(commonMessage)) {
        const message = commonMessage.join('\n');
        message && result.push({type: CommentType.COMMON, message: message, key: Math.random()});
    }
    if (!isEmpty(intelligentCodeMessage)) {
        const endIndex = intelligentCodeMessage.length - 1;
        if (!intelligentCodeMessage[endIndex].endsWith('```')) {
            intelligentCodeMessage[endIndex] = intelligentCodeMessage[endIndex] + '```';
        }
        result.push({
            type: CommentType.INTELLIGENT_CODE,
            message: intelligentCodeMessage.join('\n'),
            key: Math.random(),
        });
    }
    result.forEach(item => {
        if (item.type === CommentType.COMMON) {
            finalResult.push(...analyzeCommentSuggestion(item.message));
        }
        else {
            finalResult.push(item);
        }
    });
    let finalResultCount = 0;
    finalResult.forEach(item => {
        if (item.type !== CommentType.COMMON) {
            item.index = finalResultCount;
            finalResultCount++;
        }
    });
    return finalResult;
};

export function transformToGerritRange(range?: CommentRange): GerritCommentRange | undefined {
    if (!range) {
        return undefined;
    }
    return {
        'end_character': range.endCharacter,
        'end_line': range.endLine,
        'start_character': range.startCharacter,
        'start_line': range.startLine,
    };
}
