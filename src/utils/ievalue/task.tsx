/* eslint-disable max-lines */
import {Tooltip, Typography} from 'antd';
import {intersection} from 'lodash';
import {css} from '@emotion/css';
import {ModelItem} from '@/api/ievalue/model';
import {FlexLayout} from '@/components/ievalue/FlexLayout';
import {
    ModelEffectiveEnum,
    ModelEffectiveEnumMap,
    ModelSpaceCodeEnum,
    ModelTagColorEnumMap,
    TargetColorEnumMap,
    ModelTagEnum,
} from '@/constants/ievalue/model';
import {
    TargetEnum,
    TaskStagMap,
    TaskStageEnum,
    TaskStatusMap,
    TemplateColorEnumMap,
} from '@/constants/ievalue/task';
import {ModelOptionItem} from '@/types/ievalue/task';
import {Help} from '@/design/Help';
import {TaskTemplate} from '@/api/ievalue/task';
import {HotModelTag} from '@/components/ievalue/HotModelTag';
import {ComingOfflineModelTag} from '@/components/ievalue/ComingOfflineModelTag';

// eslint-disable-next-line complexity
export const getTaskStatus = (stage: string, stageStatus: string) => {
    switch (stage) {
        case TaskStageEnum.NEW:
        case TaskStageEnum.TERMINATED:
        case TaskStageEnum.FINISHED:
            return TaskStagMap[stage];
        case TaskStageEnum.AUTOCHECK:
        case TaskStageEnum.PREDICTING:
        case TaskStageEnum.EVALUATING:
        case TaskStageEnum.AUTO_EVALUATE:
        case TaskStageEnum.AUDITING:
        case TaskStageEnum.AUDITING_FORWARD:
        case TaskStageEnum.ACCEPTING:
            return `${TaskStagMap[stage]}${TaskStatusMap[stageStatus] ?? ''}`;
        default:
            return '-';
    }
};

export const convertObjectToMarkdownTable = (
    obj: {[key: string]: string},
    sortList?: string[]
) => {
    if (Object.keys(obj).length === 0 || typeof obj !== 'object') {
        return '';
    }
    const buildStr = (str: string[]) =>
        '|'
        + str.map(s => ` ${`${s}`.replace(/\n/g, '<br>')} |`).join('')
        + ' \n ';

    const objectKeys = sortList
        ? intersection(sortList, Object.keys(obj))
        : Object.keys(obj);
    const objectValues = objectKeys.map(key => obj?.[key]);
    const markdown =
        buildStr(objectKeys)
        + buildStr(Array(objectKeys.length).fill('---'))
        + buildStr(objectValues);
    return markdown;
};

// @ts-ignore
export const getImgUrls = (data: any, imgs: string[]) => {
    if (!data) {
        return [];
    }
    if (typeof data === 'object' && Object.keys(data).length > 0) {
        Object.keys(data).forEach(key => {
            const value = data[key];
            getImgUrls(value, imgs);
        });
    } else if (typeof data === 'string' && data.startsWith('http')) {
        imgs.push(data);
    } else if (
        typeof data === 'object'
        && data instanceof Array
        && data.length > 0
    ) {
        data.forEach(item => {
            getImgUrls(item, imgs);
        });
    }
};

const labelCss = css`
    font-size: 10px;
    line-height: 10px;
    padding: 2px 4px;
    color: white;
    opacity: 0.9;
    flex: 0 0 50px;
    text-align: center;
`;

// 模型options样式转换
export function convertModelOption(item: ModelItem) {
    const disabled = item?.isEffective !== ModelEffectiveEnum.ONLINE;
    const disabledReason = disabled ? ModelEffectiveEnumMap[item?.isEffective] : undefined;

    return {
        ...item,
        label: (
            <FlexLayout gap={16} align="center" justify="space-between" style={{width: '100%'}}>
                <FlexLayout style={{flex: 1, boxSizing: 'border-box', overflow: 'hidden'}} align="center">
                    <Typography.Text
                        disabled={disabled}
                        ellipsis={{tooltip: `${item.model}(${item.name})`}}
                    >
                        {[
                            ModelSpaceCodeEnum.Public,
                            ModelSpaceCodeEnum.Public_fee,
                        ].includes(item.spaceCode as ModelSpaceCodeEnum)
                            ? item.name
                            : `${item.model}(${item.name})`}
                    </Typography.Text>

                    <HotModelTag model={item} />
                    <ComingOfflineModelTag model={item} />
                </FlexLayout>
                <FlexLayout gap={16}>
                    {disabled && <Help>{disabledReason}</Help>}

                    {item?.tags?.map(tag => (
                        <span
                            key={tag}
                            style={{backgroundColor: ModelTagColorEnumMap[tag as ModelTagEnum]}}
                            className={labelCss}
                        >
                            {tag}
                        </span>
                    ))}
                    <span
                        className={labelCss}
                        style={{
                            backgroundColor: TargetColorEnumMap[item.target],
                        }}
                    >
                        {item.target}
                    </span>
                </FlexLayout>

            </FlexLayout>
        ),
        disabled: disabled,
        value: item.ID,
        modelName: item.name,
        modelID: item.ID,
        disabledReason,
    } as ModelOptionItem;
}

// 流程options样式转换
export function convertTemplateOption(item: TaskTemplate) {
    const tooltip = item?.stages?.map(i => i.stageName).join('->');
    return {
        ...item,
        label: (
            <FlexLayout
                gap={16}
                align="center"
                style={{minWidth: 200, width: '100%'}}
            >
                <Tooltip title={tooltip}>
                    <Typography.Text style={{flex: '1 1'}} ellipsis>
                        {item.name}
                    </Typography.Text>
                </Tooltip>
                <span
                    style={{
                        backgroundColor:
                            TemplateColorEnumMap[item.target as TargetEnum],
                        fontSize: '10px',
                        lineHeight: '10px',
                        padding: '2px 4px',
                        color: 'white',
                        opacity: '0.9',
                        flex: '0 0 50px',
                        textAlign: 'center',
                    }}
                >
                    {item.target}
                </span>
            </FlexLayout>
        ),
    };
}

export function getModelGroupNameBySpaceCode(spaceCode: string) {
    switch (spaceCode) {
        case ModelSpaceCodeEnum.Public:
            return '试用模型';
        case ModelSpaceCodeEnum.Public_fee:
            return '收费模型';
        default:
            return '注册模型';
    }
}
