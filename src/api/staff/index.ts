/* eslint-disable */
import {APP_IS_ICLOUD, APP_IS_DEV, APP_IS_ONLINE_PRODUCTION} from '@/constants/app';
import {Agent, Chat, ConversationByLabel} from '@/types/staff/chat';
import {ChatAction} from '@/types/staff/element';
import {createHomeInterface} from '@/utils/createInterface/home';
import {createStaffInterface} from '@/utils/createInterface/staff';

interface ConversationIdParam {
    conversationId: string;
}

interface AgentIdParam {
    agentId?: number;
}

type ConversationIdAndAgentId = ConversationIdParam & AgentIdParam;

export const apiGetChatById = createStaffInterface<ConversationIdParam, Chat>(
    'GET',
    '/agentic-infra/rest/v1/conversation/by-id'
);

interface GetAgentListParam {
    app?: string;
}

export const apiGetAgentList = createStaffInterface<GetAgentListParam, Agent[]>(
    'GET',
    '/agentic-infra/rest/v1/agent/list'
);

export interface ChatListFilterParam {
    buildStatus?: string[];
    agentId?: number[];
    delegationType?: string[];
    adoptStatus?: string[];
    timeRange?: string[];
    keyword?: string;
}

export interface GetChatListParam {
    /**
     * INITIAL,
     * ADOPTED,
     * REJECTED;
     */
    adoptStatus?: string;
    /**
     * SUCCESS,
     * PENDING,
     * WAITING,
     * CANCEL,
     * FAILED,
     * RUNNING;
     */
    buildStatus?: string;
    /**
     * DETAIL_PAGE,
     * AUTO,
     * INFOFLOW_DIALOG,
     * INFOFLOW_CARD;
     */
    delegationType?: string;
    /**
     * 当前只能根据title过滤
     */
    keyword?: string;
    agentId?: string;
    /**
     * 0开始，默认0；
     */
    page?: number;
    /**
     * 默认100；
     */
    size?: number;
}

export interface GetChatListRes {
    data: Chat[];
    pages: number;
    total: number;
}

export const apiGetChatList = createStaffInterface<
    GetChatListParam,
    GetChatListRes
>('GET', '/agentic-infra/rest/v1/conversation/list');

interface Element {
    content: string;
    type: string;
}
interface apiPostChatMessageParams extends ConversationIdAndAgentId {
    // 和后端商量一下合并
    fromCallback?: boolean;
    from?: 'user' | 'callback' | 'app';
    elements: Element[];
    kuContent?: Array<{
        title: string;
        markdown: string;
        link: string;
    }>;
}

export const apiPostChatMessage = createStaffInterface<
    apiPostChatMessageParams,
    void
>('POST', '/agentic-infra/rest/v1/message');

interface PostChatActionTriggerParams extends ConversationIdAndAgentId {
    action: ChatAction;
    messageId: string;
}

export const apiPostChatActionTrigger = createStaffInterface<
    PostChatActionTriggerParams,
    void
>('POST', '/agentic-infra/rest/v1/action/trigger');

export const apiPostConversationCancel = createStaffInterface<
    ConversationIdParam,
    void
>('POST', '/agentic-infra/rest/v1/conversation/cancel');

interface PostMessageCancelParams extends ConversationIdParam {
    messageId: string;
}
export const apiPostMessageCancel = createStaffInterface<
    PostMessageCancelParams,
    void
>('POST', '/agentic-infra/rest/v1/message/cancel');

interface Message {
    elements: Element[];
    kuContent?: Array<{
        title: string;
        markdown: string;
        link: string;
    }>;
}

interface PostConversationGenerateIdParams extends AgentIdParam {
    title: string;
    message: Message;
}

export const apiPostConversationGenerateId = createStaffInterface<
    PostConversationGenerateIdParams,
    Chat
>('POST', '/agentic-infra/rest/v1/conversation/generate-id');

interface GetAgentByIdParams {
    id: number;
}

export const apiGetAgentById = createStaffInterface<GetAgentByIdParams, Agent>(
    'GET',
    '/agentic-infra/rest/v1/agent/by-id'
);

interface GetAgentByLabelsParams {
    labels: string[];
    username: string;
    displayType: 'app' | 'overview'
}

export const apiGetConversationByLabels = createStaffInterface<GetAgentByLabelsParams, ConversationByLabel[]>(
    'POST',
    '/agentic-infra/rest/v1/conversation/by-labels'
);
interface GetUserLogoutResponse {
    code: number;
    message: string;
    redirectUrl: string;
}

export const apiGetUserLogout = createHomeInterface<void, GetUserLogoutResponse>(
    'GET',
    '/v1/logout'
);

export const apiPostReport = createStaffInterface<
    {
        agentId: number;
        metricName: 'taskResultView';
        metricValue: number;
        labels: Array<{
            key: 'conversationId' | 'taskId';
            value: string;
        }>;
    },
    void
>('POST', '/agentic-infra/rest/v1/metric/report');

interface UGateTokenParams {
    targetAppKey: string;
}
type Token = string;

/**
 * 获取 uuap 跨平台登录 token
 * 文档 https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/j_xaPCw1aC/j75aH07rVdAZO2
 */
export const rawGetUGateToken = createStaffInterface<UGateTokenParams, Token>(
    'GET',
    '/agentic-infra/rest/v1/cloud/token'
);

// 通用 token 获取函数
const getTokenForAppSDK = async (targetAppKey: string): Promise<string> => {
    return rawGetUGateToken({
        targetAppKey,
    });
};

const FLOW_APP_KEY_PROD = 'uuapclient-802527276960509953-J7mz7-online';
const FLOW_APP_KEY_BETA = 'uuapclient-805161162121830401-2uP85-beta';
const KU_APP_KEY_PROD = 'uuapclient-584729304643403777-ksdOg';
const KU_APP_KEY_BETA = 'uuapclient-576137589750009856-zBIDe';


export const fetchTokenForAppSDK = async (targetAppKey: string): Promise<string> => {
    if (APP_IS_ICLOUD || APP_IS_DEV) {
        return getTokenForAppSDK(targetAppKey);
    }

    return new Promise<string>((resolve, reject) => {
        const timeoutId = setTimeout(async () => {
            try {
                const token = await getTokenForAppSDK(targetAppKey);
                resolve(token);
            } catch (error) {
                reject(error);
            }
        }, 1000);

        const messageHandler = (event: MessageEvent) => {
            if (
                !event.data
                || event.data.type !== 'DE_FLOW_TOKEN_RESPONSE'
                || event.data.targetAppKey !== targetAppKey
            ) {
                return;
            }
            clearTimeout(timeoutId);
            window.removeEventListener('message', messageHandler);
            resolve(event.data.token);
        };

        window.addEventListener('message', messageHandler);
        window.parent.postMessage({type: 'DE_FLOW_TOKEN_REQUEST', targetAppKey}, '*');
    });
};

export const fetchTokenForFlowSDK = () =>
    fetchTokenForAppSDK(APP_IS_ONLINE_PRODUCTION ? FLOW_APP_KEY_PROD : FLOW_APP_KEY_BETA);

export const fetchTokenForKuSDK = () =>
    fetchTokenForAppSDK(APP_IS_ONLINE_PRODUCTION ? KU_APP_KEY_PROD : KU_APP_KEY_BETA);

