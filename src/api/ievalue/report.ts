/* eslint-disable max-lines */
import {CooperateTypeEnum, TaskEvaluateModeEnum} from '@/constants/ievalue/task';
import {
    EvaluateDetailFormColumnResponse,
    EvaluateDetailFormExpand,
    EvaluateDetailListResponse,
    ParamsEvaluateDetailListDetail,
    ParamsPostEvaluateDetailFormExpand,
} from '@/types/ievalue/report';
import {APP_WEBSOCKET_PREFIX} from '@/constants/app';
import {createOnceWebSocket} from '@/utils/createInterface/createOnceWebSocket';
import {createInterface} from './axios';
import {EvaluateEvaluatorItem} from './evaluate';
import {PromptVersionVersionItem} from './prompt-version';
import {StagetegyItem} from './case';

interface ReportRes<T> {
    reports: T[];
    total: number;
}

export interface ReportListParams {
    spaceCode?: string;
    datasetName?: string;
    comeFrom?: string;
    creator?: string;
    stage?: string;
    startTime?: string;
    endTime?: string;
    reportName?: string;
    pn?: number;
    size?: number;
    order?: string;
}

interface ReportListRes {
    total: number;
    list?: ReportItem[];
    reports?: ReportItem[];
}

interface ReportItem {
    ID: number;
    reportName: string;
    datasetName: string;
    url: string;
    creator: string;
    spaceCode: string;
    createTime: string;
    taskID: number;
    stage: string;
    comeFrom: string;
    datasetID: number;
}

/**
 * 查看报告列表
 */
export const apiReportList = createInterface<ReportListParams, ReportListRes>(
    'GET',
    '/report/list'
);
export const apiTaskReportList = createInterface<
    ReportListParams,
    ReportListRes
>('GET', '/common/report/list');
export const apiCompareReportList = createInterface<
    ReportListParams,
    ReportListRes
>('GET', '/evaluate/compare/dashboard/list');

export interface MergeReportItem {
    reportId: number;
    reportName: string;
    taskNames: string[];
    creator: string;
    createTime: string;
}

export const apiMergeReportList = createInterface<
    ReportListParams,
    ReportRes<MergeReportItem>
>('GET', '/evaluate/merge/dashboard/list');

export const apiMergeReportDetail = createInterface<
    {reportID: string, spaceCode: string},
    any
>('POST', '/evaluate/merge/dashboard/detail');

export interface EvaluateParams {
    taskID: number;
}

export interface IEvaluateDetail {
    taskID: number;
    taskName: string;
    taskTarget: string;
    evaluateMode: TaskEvaluateModeEnum;
    cooperateType: CooperateTypeEnum;
    policyName: string;
    evaluateMetric: string[];
    desc: string;
    order: string;
    taskPolicyName: string;
    policyDesc?: string;
    note: string;
    conclusion?: string;
    pass: number;
    datasetID?: number;
    spaceCode?: string;
    evaluateSet?: IEvaluateSet;
    score?: any;
    gsb?: any;
    reportFormat?: string;
}
export interface IEvaluateSet {
    name: string;
    url: string;
    datasetID?: number;
    spaceCode?: string;
}

export interface IDataMap {
    label: string;
    value: string;
}

/**
 * 据任务ID查看task元数据信息及评估结论
 */
export const apiEvaluateDetailList = createInterface<
    EvaluateParams,
    IEvaluateDetail
>('GET', '/evaluate/detail/list');

/**
 * 获取可选的BI模板列表
 */
export interface CustombiTemplateParams {
    spaceCode: string;
}
export const apiCustombiTemplateList = createInterface<
    CustombiTemplateParams,
    any
>('GET', '/custombi/template/list');

export interface IEvaluateUpdateParams {
    taskID?: number;
    conclusion?: string;
    pass?: number; // 0:初始值；1:通过；2:不通过
}
export interface IEvaluateUpdateRes {
    conclusion: string;
    pass: number; // 0:初始值；1:通过；2:不通过
}
/**
 * 更新评估结论及是否通过
 */
export const apiEvaluateUpdate = createInterface<
    IEvaluateUpdateParams,
    IEvaluateUpdateRes
>('POST', '/evaluate/conclusion/update');

export interface IDashboardDetail {
    caseCount: number;
    groupCount: number;
    sugarAddress: string;
    target: Target[];
}

export interface Target {
    model: string;
    dimensionData?: DimensionData[];
    promptVersion?: PromptVersionVersionItem;
}

export interface DimensionData {
    name: string;
    data: Datum[];
}

export interface Datum {
    name: string;
    count: number;
}

/**
 * 更新评估结论及是否通过
 */
export const apiEvaluateDashboardList = createInterface<
    EvaluateParams,
    IDashboardDetail
>('GET', '/evaluate/dashboard/list');

export interface EvaluateFormParams {
    taskID: string[];
    pn: string[];
    size: string[];
}

interface EvaluateFormColumnItem {
    name: string;
    value: string[];
}

export interface EvaluateFormResponse {
    total: number;
    pn: number;
    size: number;
    column: EvaluateFormColumnItem[];
    list: any[];
}

export const apiEvaluateFormList = createInterface<
    EvaluateFormParams,
    EvaluateFormResponse
>('POST', '/evaluate/form/list');

interface EvaluateFormNotesUpdateParams {
    ID: string;
    notes: string;
}

export const apiEvaluateFormNotesUpdate = createInterface<
    EvaluateFormNotesUpdateParams,
    object
>('PUT', '/evaluate/form/notes/update');

export interface EvaluateDashboardSatisfactionParams {
    taskID: number;
    hasL0?: string;
    hasL1?: string;
    type?: string;
}

export interface HeaderItem {
    name: string;
    children: string[];
}

export interface EvaluateDashboardSatisfactionResponse {
    body: any[];
    headers: any[];
}

// 评估报告满意度汇总
export const apiEvaluateDashboardSatisfaction = createInterface<
    EvaluateDashboardSatisfactionParams,
    EvaluateDashboardSatisfactionResponse
>('GET', '/evaluate/dashboard/satisfaction');

// 评估报告GSB汇总
export const apiEvaluateDashboardGSB = createInterface<
    EvaluateDashboardSatisfactionParams,
    EvaluateDashboardSatisfactionResponse
>('GET', '/evaluate/dashboard/gsb');

export interface CompareReportListParams {
    spaceCode: string;
}
export interface CompareReportItem {
    taskId: number;
    taskName: string;
    evaluateDataset: string;
    evaluatePolicy: string;
    creator: string;
    createTime: string;
}
interface CompareReportListRes {
    total: number;
    reports: CompareReportItem[];
}

interface CreateCompareReportParams {
    spaceCode: string;
    baseTaskID: any;
    compareTaskIDs: any[];
    reportName: string;
}

/**
 * 查看对比报告列表
 */
export const apiCompareReportSelectList = createInterface<
    CompareReportListParams,
    CompareReportListRes
>('GET', '/evaluate/compare/dashboard/select');

export const apiCreateCompareReport = createInterface<
    CreateCompareReportParams,
    any
>('POST', '/evaluate/compare/dashboard/create');

interface indicatorDimDataItem {
    indicator: string;
    score: number;
    choices: any;
}

interface IndicatorScoreItem {
    indicatorDimData: indicatorDimDataItem[];
    model: string;
    mapModelName: string;
}

/**
 * 查看各维度的聚合数据
 */
export const apiIndicatorScore = createInterface<
    {taskID: number},
    {target: IndicatorScoreItem[]}
>('GET', '/evaluate/dashboard/indicator/score');

interface DeleteCompareReportParams {
    spaceCode: string;
    reportID: any;
}
export const apiDeleteCompareReport = createInterface<
    DeleteCompareReportParams,
    any
>('DELETE', '/evaluate/compare/dashboard');
export const apiCompareReportDetail = createInterface<
    {reportID: string, spaceCode: string},
    any
>('POST', '/evaluate/compare/dashboard/detail');

interface CreateCustombiReportParams {
    templateId: string;
    tag: string;
    taskID: number;
}

export const apiCreateCustombiReport = createInterface<
    CreateCustombiReportParams,
    any
>('POST', '/custombi/report/create');

/**
 * 查看可合并的报告列表
 */
export const apiMergeReportSelectList = createInterface<
    CompareReportListParams,
    CompareReportListRes
>('GET', '/evaluate/merge/dashboard/select');

interface MergeReportCreateParams {
    spaceCode: string;
    taskIDs: number[];
    reportName: string;
}

/**
 * 创建合并报告任务
 */
export const apiMergeReportCreate = createInterface<
    MergeReportCreateParams,
    any
>('POST', '/evaluate/merge/dashboard/create');

interface MergeDashboardSingleMultiRoundParams {
    taskIDs: string;
    reportName: string;
}

/**
 * 生成单轮多轮合并报告
 */
export const apiMergeDashboardSingleMultiRound = createInterface<
    MergeDashboardSingleMultiRoundParams,
    string
>('GET', '/merge/dashboard/single_multi_round');

interface StatsUsabilityItem {
    modelID: number;
    modelName: string;
    modelResult: Record<string, string>;
}

/**
 * 查看策略可用性报告
 */
export const apiStatsUsability = createInterface<
    {taskID: number},
    StatsUsabilityItem[]
>('POST', '/evaluate/result/stats/usability');

/**
 * 通过任务ID获取评估服务信息
 */
export const apiGetEvaluatorInfoByTask = createInterface<
    {taskID: number},
    EvaluateEvaluatorItem
>('GET', '/evaluate/evaluator/info/byTask');

/**
 * 查看评估详情列表
 */
export const apiEvaluateDetailListDetail = createInterface<
    ParamsEvaluateDetailListDetail,
    EvaluateDetailListResponse
>('POST', '/evaluate/form/detail');

/**
 * 查看评估详情列表
 */
export const apiEvaluateDetailFormColumn = createInterface<
    {taskID: number},
    EvaluateDetailFormColumnResponse
>('GET', '/evaluate/form/column');

/**
 * 展开评估结果集
 */
// eslint-disable-next-line max-len
export const apiPostEvaluateDetailFormExpand = createInterface<
    ParamsPostEvaluateDetailFormExpand,
    EvaluateDetailFormExpand
>('POST', '/evaluate/form/expand');

export interface PlanCompareParams {
    planRecordID: string[];
}

export interface PlanCompareRes {
    body: string[][];
    headers: string[];
    taskTemplateID?: number[];
}
/**
 * 查看不同执行记录下业务任务可用率统计报告
 */
export const apiPostPlanCompareUsabilityByTask = createInterface<
    PlanCompareParams,
    PlanCompareRes
>('POST', '/plan/compare/dashboard/usabilityByTask');

/**
 * 查看不同执行记录下评估纬度可用率统计报告
 */
export const apiPostPlanCompareUsabilityByPolicy = createInterface<
    PlanCompareParams,
    PlanCompareRes
>('POST', '/plan/compare/dashboard/usabilityByPolicy');

interface PlanCompareBasicInfoItem {
    ebModelID: string[];
    evaluateDimension: string[];
    evaluateName: string[];
    modelName: string[];
}

/**
 * 查看不同执行记录下业务任务可用率统计基本信息
 */
export const apiPostPlanCompareBasicInfo = createInterface<
    PlanCompareParams,
    PlanCompareBasicInfoItem
>('POST', '/plan/compare/dashboard/basicInfo');

interface Refinement {
    desc?: string[];
    modelID?: number[];
    score?: number[];
    taskID?: number[];
}

export interface CompareCaseUsabilityParamsBasic {
    planRecordID: number[];
    taskTemplateID: number;
}
export interface CompareCaseUsabilityParams
    extends CompareCaseUsabilityParamsBasic {
    pn: number;
    refinement: Refinement;
    size: number;
}

export interface HeaderRefinement {
    caseID: number;
    taskID: number;
}
interface Header {
    name: string;
    refinement: HeaderRefinement;
    isModified?: boolean;
}

interface CompareCaseUsabilityResponse {
    body: any[];
    headers: Header[];
    total: number;
}

/**
 * 查看相同数据集下不同任务之间case级别的对比数据
 */
export const apiPostCompareDashboardCaseUsability = createInterface<
    CompareCaseUsabilityParams,
    CompareCaseUsabilityResponse
>('POST', '/plan/compare/dashboard/case/usability');

interface ModelRefinement {
    modelID: number;
    taskID: number;
}
interface ModelFilterableItem {
    name: string;
    refinement: ModelRefinement;
}

interface CompareDashboardFilterableResponse {
    desc: string[];
    models: ModelFilterableItem[];
    score: number[];
}

/**
 * 查看case级别对比报告的可筛选项
 */
export const apiPostCompareDashboardFilterableItems = createInterface<
    CompareCaseUsabilityParamsBasic,
    CompareDashboardFilterableResponse
>('POST', '/plan/compare/dashboard/filterable/items');

interface CompareScoreUpdateParams {
    line: number;
    refinement: HeaderRefinement;
    value: number;
}

/**
 * 查看case级别对比报告的可筛选项
 */
export const apiPostCompareScoreUpdate = createInterface<
    CompareScoreUpdateParams,
    void
>('POST', '/plan/compare/dashboard/score/update');

export interface EvaluateDashboardMetricParams extends EvaluateDashboardSatisfactionParams {
    metric: string;
    metricName: string;
}
/**
 * 查看指定指标的统计数据
 */
export const apiGetEvaluateDashboardMetric = createInterface<
    EvaluateDashboardMetricParams,
    EvaluateDashboardSatisfactionResponse
>('GET', '/evaluate/dashboard/metric');

// 查看rank统计数据
export const apiEvaluateDashboardRank = createInterface<
    EvaluateDashboardSatisfactionParams,
    EvaluateDashboardSatisfactionResponse
>('GET', '/evaluate/dashboard/rank');

export enum StatisticLevelEnum {
    Session = 'session',
    Turn = 'turn',
}

interface EvaluateDashboardTagParams extends EvaluateDashboardSatisfactionParams {
    statisticLevel: StatisticLevelEnum;
}

// 查看tag统计数据
export const apiEvaluateDashboardTag = createInterface<
    EvaluateDashboardTagParams,
    EvaluateDashboardSatisfactionResponse
>('GET', '/evaluate/dashboard/tag');

// 查看tag占比类型的统计指标数据
export const apiEvaluateDashboardChatTagProportion = createInterface<
    EvaluateDashboardTagParams,
    EvaluateDashboardSatisfactionResponse
>('GET', '/evaluate/dashboard/tag/proportion');

// 查看tag频次类型的统计指标数据
export const apiEvaluateDashboardChatTagFrequency = createInterface<
    EvaluateDashboardTagParams,
    EvaluateDashboardSatisfactionResponse[]
>('GET', '/evaluate/dashboard/tag/frequency');

// 查看准确度统计数据
export const apiEvaluateDashboardAccuracy = createInterface<
    {taskID: number},
    EvaluateDashboardSatisfactionResponse
>('GET', '/evaluate/dashboard/accuracy');

// 查看专项能力满意度
export const apiEvaluateDashboardSpecialSatisfaction = createInterface<
    {taskID: number},
    EvaluateDashboardSatisfactionResponse[]
>('GET', '/evaluate/dashboard/special_satisfaction');

export interface SummaryTaskModelItem {
    modelID: number;
    modelName: string;
}
export interface SummaryTaskModelFilterItem {
    modelList: SummaryTaskModelItem[];
    taskID: number;
    taskName: string;
}

export interface SummaryTasklFilterItem {
    l0: string;
    l1: string[];
}

export interface SummaryTaskFilterItem {
    modelFilter: SummaryTaskModelFilterItem[];
    lFilter: SummaryTasklFilterItem[];
}

interface SummaryTaskFilterResponse {
    filters: SummaryTaskFilterItem;
}

// 查看汇总任务筛选信息
export const apiGetSummaryTaskFilter = createInterface<
    {taskID: string},
    SummaryTaskFilterResponse
>('GET', '/summary/task/filter');

export interface ReportEvaluateTaskListParams {
    datasetName?: string;
    endTime?: string;
    /**
     * 是否是我创建的
     */
    isCreator?: boolean;
    /**
     * 是否是我负责的
     */
    isOwner?: boolean;
    pn?: number;
    policyName?: string;
    size?: number;
    startTime?: string;
}

export interface ReportEvaluateTaskItem {
    createTime?: string;
    creator?: string;
    completedRate?: string;
    datasetID?: string;
    dataset?: number;
    policy?: string;
    policyID?: number;
    stage?: string;
    taskName?: string;
    target?: string;
    taskID?: number;
}

interface ReportEvaluateTaskListRes {
    total: number;
    taskList: ReportEvaluateTaskItem[];
}

/**
 * 创建报告查询任务列表
 */
export const apiReportEvaluateTaskList = createInterface<
    ReportEvaluateTaskListParams,
    ReportEvaluateTaskListRes
>('POST', '/evaluate/task/list');

interface SummaryReportCreateParams {
    creator: string;
    filterInfo: SummaryTaskFilterItem;
    spaceCode: string;
    summaryReportName: string;
}

export interface SummaryReportCreateRes {
    createTime: string;
    creator: string;
    filterInfo: SummaryTaskFilterItem;
    ID: number;
    policyID: number;
    reportUrl: string;
    spaceCode: string;
    summaryReportName: string;
    taskID: string;
}

/**
 * 创建汇总报告
 */
export const apiSummaryReportCreate = createOnceWebSocket<
    SummaryReportCreateParams,
    SummaryReportCreateRes
>(`${APP_WEBSOCKET_PREFIX}/api/ievalue/api/v1/ws/summary/report/create`);

export interface SummaryReportItem {
    createTime: string;
    creator: string;
    ID: string;
    reportUrl: string;
    summaryReportName: string;
    taskName: string[];
    filterInfo?: SummaryTaskFilterItem;
    policyInfo?: StagetegyItem;
}

export const apiSummaryReportList = createInterface<
    ReportListParams,
    ReportRes<SummaryReportItem>
>('GET', '/evaluate/summary/report/list');

export const apiSummaryReportInfo = createInterface<
    {summaryReportID: number},
    SummaryReportItem
>('GET', '/evaluate/summary/report/info');

interface SummaryReportParams {
    summaryReportID: number;
    hasL0?: string;
    hasL1?: string;
}

export interface SummaryDashboardTagParams extends SummaryReportParams {
    statisticLevel: StatisticLevelEnum;
}

export interface SummaryDashboardMetricParams extends SummaryReportParams {
    metric: string;
    metricName: string;
}

/**
 * 查看汇总报告指定指标的统计数据
 */
export const apiGetSummaryDashboardMetric = createInterface<
    SummaryDashboardMetricParams,
    EvaluateDashboardSatisfactionResponse
>('GET', '/summary/dashboard/metric');

// 查看汇总报告tag统计数据
export const apiGetSummaryDashboardTag = createInterface<
    SummaryDashboardTagParams,
    EvaluateDashboardSatisfactionResponse
>('GET', '/summary/dashboard/tag');

// 查看汇总报告tag频次类型的统计指标数据
export const apiGetSummaryDashboardTagFrequency = createInterface<
    SummaryDashboardTagParams,
    EvaluateDashboardSatisfactionResponse[]
>('GET', '/summary/dashboard/tag/frequency');

// 查看tag占比类型的统计指标数据
export const apiSummaryDashboardTagProportion = createInterface<
    SummaryDashboardTagParams,
    EvaluateDashboardSatisfactionResponse
>('GET', '/summary/dashboard/tag/proportion');
export interface EvaluateDashboardTypeListItem {
    name: string;
    type: string;
}
export const apiEvaluateDashboardTypeList = createInterface<
    {taskID: number},
    EvaluateDashboardTypeListItem[]
>('GET', '/evaluate/dashboard/type/list');

interface EvaluateDashboardHumanEfficiencyParams {
    taskID: number;
    stageIDs: number[];
    hourGap?: number;
}

interface YDataItem {
    name: string;
    numberList: number[];
}

interface EvaluateDashboardHumanEfficiencyRes {
    xData: number[];
    yData: YDataItem[];
}

/**
 * 获取报表界面人效统计数据
 */
export const apiEvaluateDashboardHumanEfficiency = createInterface<
    EvaluateDashboardHumanEfficiencyParams,
    EvaluateDashboardHumanEfficiencyRes
>('POST', '/evaluate/dashboard/human/efficiency');
