/**
 * @file 合并页面相关API
 */
import {createInterface} from '@/utils/icode/api/createInterface';
import {CanAutoMerge, MergeCompareInfo} from '@/types/icode/repo';
import {ParamsMerge} from '@/types/icode/params';

// 获取合并时候用到的ahead、behind以及提交的相关信息
export const apiGetMergeCompareInfo = createInterface<ParamsMerge, MergeCompareInfo>(
    'GET',
    '/rest/git/compare'
);

// 获取当前两个分支比对结果，是否可以操作等等
export const apiGetCanAutoMerge = createInterface<ParamsMerge, CanAutoMerge>(
    'GET',
    '/rest/git/merge/canAutoMerge',
    {
        // NOTE 可能是不必要的覆盖
        onResolve: ({data}) => data,
    }
);

// 进行合并操作
export const apiGetMergeResult = createInterface<ParamsMerge>(
    'GET',
    '/rest/git/merge/merge'
);

export interface ParamsGetMergeRequestExist {
    sourceBranch: string;
    targetBranch: string;
    repo: string;
}

interface ResultGetMergeRequestExist {
    isExist: boolean;
    changeId: number;
}

export const apiGetMergeRequestExist = createInterface<ParamsGetMergeRequestExist, ResultGetMergeRequestExist>(
    'GET',
    '/rest/review/mergeRequest/isExist'
);

interface ParamsPostMergeRequest {
    sourceBranch: string;
    targetBranch: string;
    project: string;
    subject: string;
    allowConflicts?: boolean;
}

interface ResultPostMergeRequest {
    _number: number;
}

export const apiPostMergeRequest = createInterface<ParamsPostMergeRequest, ResultPostMergeRequest>(
    'POST',
    '/rest/review/mergeRequest'
);

// 获取两个分支/commit的 merge-base commit
export const apiGetMergeBase = createInterface<ParamsMerge, string>(
    'GET',
    '/rest/git/compare/mergeBase'
);
