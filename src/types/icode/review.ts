/* eslint-disable max-lines */
import {Comment, MessageInfo, CommentType} from '@/types/icode/comment';

export interface Summary {
    succeed: string[];
    failed: string[];
    running: string[];
    waiting: string[];
    block: string[];
}

export interface Reviewer {
    name: string;
    value: number;
    date: string;
}

export type JobStatus = 'SUCCESS' | 'STAGE' | 'FAILED' | 'NONE' | 'INITIALIZING' | 'CHECKING';

export type JobClassify = 'STATIC_CHECK'
    | 'SECURITY_CHECK'
    | 'DUPLICATE_CHECK'
    | 'CODE_STYLE'
    | 'SA_CHECK'
    | 'MAINTAINABILITY'
    | 'STABLE_BUILD_CHECK'
    | 'MISRA'
    | 'OSC_CHECK'
    | 'UT'
    | 'STATIC_CODE_CHECK'
    | 'FILE_UTF8_CHECK'
    | 'STABLEAPI_CHECK'
    | 'LANGUAGE_CHECK'
    | 'COMMENT_RATE_CHECK'
    | 'UT_GENERATE';

interface CheckUrls {
    checker: string;
    url: string | null;
}

export interface ReviewChangeOwner {
    name: string;
    'display_name': string;
    email: string;
    type: string;
    username: string;
}

export interface ReviewChangeLabel {
    Verified: never;
    'Code-Review': {
        all?: Reviewer[];
    };
}

export interface ReviewChange {
    id: string | null;
    project: string;
    branch: string;
    topic: string | null;
    'change_id': null;
    subject: string;
    created: string;
    updated: string;
    starred: null;
    reviewed: null;
    mergeable: null;
    insertions: null;
    deletions: null;
    owner: ReviewChangeOwner;
    labels: ReviewChangeLabel;
    messages: null;
    'current_revision': string;
    revisions: null;
    status: string;
    'source_branch'?: string;
    'change_labels'?: unknown[];
    _number: number;
    _sortkey: null;
}

export interface ReviewChangeTask {
    'check_summaries': unknown[];
    pipelines: unknown[];
    _number: number;
}

export type ChangeStatus = 'NEW' | 'MERGED' | 'MERGE_CONFLICT' | 'ABANDONED' | 'CR_DENIED' | 'CR_NEED';

export interface GlobalReviewer {
    codeReview: {
        email: string;
        name: string;
        username: string;
        value: number;
    };
    email: string;
    name: string;
    username: string;
    value: number;
    robot: boolean;
    isOwner?: boolean;
}

export interface ChangeInfoPipeline {
    changeId: number;
    pipelineUrl: string | null;
    currentStageNum: number;
    result: JobStatus;
    stageCount: number;
    isPipelineExempted?: boolean;
    pipelineName: string;
    repoPath?: string;
    isBlock: boolean;
    timeDiff: number;
    pipelineBuildId: number;
    pipelineConfId: number;
    triggerMode: string;
    gmtCreate: string;
    stageId: number;
}

export interface ChangeInfoPipelineExempted {
    isPipelineExempted: boolean;
}

interface LatestPatchSetHashTag {
    action: 'create' | 'publish';
    actionType: 'CONFLICT' | 'AUTO_FIX' | 'CODE_TO_COMMENT';
    activityType: string;
    patchSetId: number;
    user: string;
}

export interface ChangeInfoRevisionTip {
    changeId: string;
    patchSetId: string;
    revision: string;
}

/* eslint-disable @typescript-eslint/no-explicit-any */
export interface ChangeInfo {
    currentRevision: string;
    revisionId: string;
    project: string;
    branch: string;
    sourceBranch?: string;
    topic: string;
    changeId: string;
    subject: string;
    status: string;
    created: string;
    updated: string;
    mergeable: boolean;
    owner: ReviewChangeOwner;
    labels: ReviewChangeLabel;
    changeStatus: ChangeStatus;
    permittedLabels: false;
    firstChangeStatus: 'ABANDONED' | 'MERGED';
    pipelineInfo: {
        noBuildReason?: string;
        exemptInfo: {
            operator: string;
        } | null;
        status: string;
        changePipelines: ChangeInfoPipeline[];
        revisionId: string;
        changeId: number;
        hasPipelines: boolean;
    };
    pipelines: ChangeInfoPipeline[] | ChangeInfoPipelineExempted[];
    revisionTip: ChangeInfoRevisionTip;
    revisionFirst: never;
    ignoreAgileScore: boolean;
    autoSubmit: boolean;
    scmflowBranchReleased: boolean;
    crNeed: boolean;
    forbidSelfScore: boolean;
    personal: boolean;
    submitStrategy: string;
    needStaticCheck: boolean;
    needSecurityCheck: boolean;
    needIntelligentPrediction: boolean;
    needMaintainability: boolean;
    needMisraCheck: boolean;
    needStableBuildCheck: boolean;
    needCommentRateCheck: boolean;
    needCommentRateScore: boolean;
    revisionTipCreateTimeInfo: {timeDiff: number, timeDiffString: string};
    changeRobotInfo: {ci: never, eagle: never};
    reviewId: string;
    globalMessages: boolean;
    globalReviewers: GlobalReviewer[];
    visitor: {username: string, role: string};
    _number: number;
    changeNumber: string;
    id: string;
    latestPatchSetHashTags: LatestPatchSetHashTag[];
    createdBySystem?: boolean;
    insertions: number; // 增加行数
    deletions: number; // 减少行数
    estimatedReviewTime: 5 | 15 | 30 | -1; // 预估时间，值为5，15，30 ，-1 ，其中-1表示预估时间超过30分钟，这种情况不展示预估时间
}

export interface WsPipelines {
    pipelineCheckResult: ChangeInfo | null;
    isNewComments: boolean | null;
    isNewSubmission: boolean | null;
    changeNumber: number;
    isCodeStyleAutoFixed: boolean | null;
}

interface CodeStyleResult {
    classification: string;
    columnNumber: number;
    description: string;
    filePath: string;
    lineNumber: number;
    name: string;
}

export interface Job {
    allResultCount: number;
    errorReportCount: number;
    highCount: number;
    // 下面两个属性代码里面没用到，先留着
    // middleCount: number;
    // lowCount: number;
    passCount: number;
    checkJob: {
        id: number;
        ignoreScore: boolean;
        status: JobStatus;
        // 可能没有 beginTime 但有 gmtCreate
        beginTime?: string;
        gmtCreate: string;
        checkerUrls?: CheckUrls[] | null;
        description?: string;
        taskId: number;
        codestyleFix?: boolean;
        score?: number;
    };
    checkTool: {
        classify: JobClassify;
        timeout?: number;
    };
    jobExempt: {
        exempt: boolean;
        exemptType?: 'TIME_OUT' | 'MANUAL' | 'AUTO' | null;
        userName?: string | null;
        status?: string | null;
    };
    codestyle?: {
        codeStyleFix: boolean;
        codeStyleResultInfoList?: CodeStyleResult[];
    };
    report?: string;
    canAutoFixCodeStyleIssues?: boolean;
}

export interface UtJob {
    id: number;
    gmtCreate: string;
    taskId: string;
    status: JobStatus;
    incReport: string;
    incLineRate: number;
    limitIncLineRate: number;
    totalCase: number;
    successCase: number;
    skipCase: number;
    errorCase: number;
    failCase: number;
    errorMessage: string;
}

interface UtJobResult {
    totalCase: number;
    successCase: number;
    skipCase: number;
    errorCase: number;
    failCase: number;
    incLineRate: number;
    limitIncLineRate: number;
    incReport: string;
    conversationId?: string;
    // UT 状态信息
    genIncLineRate?: string;
    processedMethod?: number;
    needGenMethod?: number;
    successGenCase?: number;
}

export interface FormatUtJob {
    status: JobStatus;
    taskId: string;
    errorMessage: string; // 也可能会返回一个字符串，用逗号隔开，分别代表旧changeNumber,单测用例生成个数，单测覆盖率提升率（后端说加新字段代价大，所以不加新字段复用了这个）
    checkTool: {
        classify: JobClassify;
        timeout?: number;
    };
    checkJob: {
        id: number;
        ignoreScore: boolean;
        status: JobStatus;
        gmtCreate: string;
        codestyleFix?: boolean;
    };
    allResultCount: number;
    highCount: number;
    jobExempt: {
        exempt: boolean;
    };
    results: UtJobResult;
}

export interface UtGenerateCheckJob {
    genIncLineRate: string;
    needGenMethod: number;
    processedMethod: number;
    successGenCase: number;
    conversationId?: string;
}

export interface UtGenerateSummary {
    line_inc_coverage: string;
    line_inc_coverage_improve: string;
    gen_success_num: number;
}

// export interface UtJob extends Omit<Job, 'errorReportCount'| 'lowCount'| 'middleCount'| 'passCount'> {
//     status: JobStatus;
//     taskId: string;
//     errorMessage: string;
//     results: {
//         totalCase: number;
//         successCase: number;
//         skipCase: number;
//         errorCase: number;
//         failCase: number;
//         incLineRate: number;
//         limitIncLineRate: number;
//         incReport: string;
//     };
// }

/* eslint-disable camelcase */
export interface IncItem {
    base_commit_id: string;
    branch: string;
    commit_id: string;
    cover_inc_branch: number;
    cover_inc_function: number;
    cover_inc_line: number;
    createdAt: string;
    diff_lines: number[];
    inc_file_num: number;
    mark_lines: {
        [key: number]: {
            cov_tag: 'fc' | 'pc' | 'nc';
            dim_type: string;
            extra_data: null;
            is_diff: boolean;
        };
    };
    module_path: string;
    percent_inc_br_cov: string;
    percent_inc_fun_cov: string;
    percent_inc_line_cov: string;
    src_md5: string;
    task_id: string;
    total_inc_branch: number;
    total_inc_function: number;
    total_inc_line: number;
}
/* eslint-enable camelcase */

export interface IncSummary {
    [key: number]: 'fc' | 'pc' | 'nc';
}

export interface CommitPipeline {
    changeId: number;
    currentStageNum: number;
    gmtCreate: string;
    gmtModified: string;
    jobId: number;
    pipelineBuildId: number;
    pipelineConfId: number;
    pipelineName: string;
    pipelineUrl: string;
    result: string;
    revisionId: string;
    stageCount: number;
    stageId: number;
    triggerMode: string;
}

export interface Revision {
    agileCi: null;
    changeId: number;
    changeKey: string;
    changeStatus: string | null;
    commitDetail: {
        uthor: null;
        branches: null;
        changeId: null;
        children: null;
        comment: string;
        commitId: string;
        commitTime: number;
        committer: {
            name: string;
            email: string;
            time: string;
        };
        parents: string[];
    } | null;
    createdOn: string;
    draft: null;
    groupList: string[];
    groups: string;
    patchSetId: number;
    pipelines: CommitPipeline[] | null;
    revision: string;
    subject: string;
    uploaderAccountId: null;
    userName: null;
}

export type UnsatisfiedReviewer = any;

export interface UnsatisfiedSubmitSetting {
    data: string[];
    isPassed: boolean;
    type: string;
}

export interface UnsatisfiedShape {
    unsatisfiedReviewers: UnsatisfiedReviewer[];
    unsatisfiedSubmitSettings: UnsatisfiedSubmitSetting[];
}

export interface ReviewComment {
    author: string;
    authorId: number;
    changeId: number;
    fileName: string;
    lineNbr: number;
    message: string;
    parentUuid: null;
    patchSetId: number;
    status: string;
    uuid: string;
    side?: number | string;
    writtenOn: string;
    // COMMON：普通评论类型 QUICK_REPLY：表情快捷回复 MUST_BE_FIXED：表情快捷回复的必须修复
    commentType?: CommentType;
    rangeEndChar: number | null;
    rangeEndLine: number | null;
    rangeStartChar: number | null;
    rangeStartLine: number | null;
    suggestionIndex?: number;
}

export interface PatchSet {
    commitId: string;
    patchSetId?: number;
    changeId?: number;
    createdOn?: string;
    index: number;
}

export interface CurrentPatchSets {
    aPatchSet: PatchSet;
    bPatchSet: PatchSet;
}

export interface ReviewActivityRecord {
    changeType: string;
    comment: string;
    lineComments: null;
    name: string;
    changeNumber: number;
    revisionNumber: number;
    score: number;
    status: string;
    updated: string;
    username: string;
    activityType?: string;
}

export interface ReviewActivityRobotItem {
    title: string;
    status: string | null;
    updated: string;
    link?: string;
}

export type ReviewActivityRecordType = 'UPLOADED_PATCH'
    | 'SUBJECT_TITLE'
    | 'REVIEW_PATCH'
    | 'REVIEW_SCORE'
    | 'ABANDONED'
    | 'RESTORED'
    | 'MERGED_REPOSITORY'
    | 'REBASED'
    | 'REMOVED_VOTES'
    | 'COMMENT'
    | 'UNFOLD'
    | 'DELETE_REVIEWER';

/* eslint-disable camelcase */
interface LineComment {
    id: string;
    in_reply_to: string | null;
    line: number;
    updated: string;
    messageInfo: MessageInfo;
    exempted: boolean | null;
    abuse: boolean | null;
    exempted_count: number;
    total_exempt_count: number;
    error_report_count: number;
    total_error_count: number;
    side?: string | null;
}
/* eslint-enable camelcase */

export interface EagleOperation {
    username: string;
    name: string;
    updated: string;
    changeType: string;
    score: number;
    status: string;
    comment: string;
    revisionNumber: number;
    lineComments: Record<string, LineComment[]>;
}

export interface PipelineOperation {
    gmtCreate: string;
    gmtModified: string;
    changeId: number;
    revisionId: string;
    pipelineConfId: number;
    pipelineName: string;
    triggerMode: string;
    pipelineBuildId: number;
    jobId: number;
    stageId: number;
    currentStageNum: number;
    stageCount: number;
    pipelineUrl: string;
    result: string;
    success: boolean;
    none: boolean;
    completed: boolean;
}

export interface PipelineHistory {
    changeNumber: number;
    revisionId: string;
    revisionNumber: 0;
    pipelines: PipelineOperation[];
}

export interface ReviewActivityRecordItem {
    type: ReviewActivityRecordType;
    updated: string;
    list?: ReviewActivityRobotItem[];
    commentList?: Comment[];
    username?: string;
    newSubject?: string;
    comment?: string;
    score?: number;
    changeNumber?: number;
    revisionNumber?: number;
    eagleJobs?: EagleOperation[];
    pipelines?: PipelineOperation[];
    remain?: number;
    reviewer?: string;
}

export interface ReviewActivitySubject {
    changeNumber: number;
    changeOwner: string;
    createdOn: string;
    id: null;
    newSubject: string;
    oldSubject: string;
    repoName: string;
}

export interface ReviewCommentStat {
    changeId: number;
    codeStyleTotal: number;
    duplicateTotal: number;
    humanTotal: number;
    intelligentPredictionTotal: number;
    maintainabilityTotal: number;
    misraTotal: number;
    myTotal: number;
    patchSetId: number;
    securityCheckTotal: number;
    staticCheckTotal: number;
    total: number;
}

export interface ReviewDraft {
    author: string;
    authorId: number;
    changeId: number;
    uuid: string;
    fileName: string;
    lineNbr: number;
    message: string;
    parentUuid: null;
    patchSetId: number;
    side: 0 | 1 | 'PARENT' | 'REVISION';
    status: string;
    writtenOn: string;
    rangeEndChar: number | null;
    rangeEndLine: number | null;
    rangeStartChar: number | null;
    rangeStartLine: number | null;
    commentType?: CommentType;
}

export interface Reviews {
    changes: ReviewChange[];
    loading: boolean;
    tasks: {
        [key: string]: ReviewChangeTask;
    };
    hasMore?: boolean;
}

export interface ReviewListNode {
    key: string;
    subject: {
        subject: string;
        branch: string;
    };
    updated: string;
    changeNumber: number;
    repo: string;
    pipelines: any[];
    score: number;
    branch: string;
    sourceBranch?: string;
    owner: ReviewChangeOwner;
    checkers: any[];
    topic: string;
    labels: any[];
    build?: any;
}

export interface ReviewFileChange {
    status: string;
    fileName: string;
    linesInserted?: number;
    linesDeleted?: number;
}

export interface MergeSetting {
    kind: 'merge' | 'squash';
    deleteSourceBranch: boolean;
}

export type AutoFixCodeStyleStatus = 'INITIALIZING'| 'SUCCESS'| 'FAILED'| 'TIME_OUT'| 'FIXING';

export interface AutoFixCodeStyleDetailResult {
    id: number;
    name: string;
    description: string;
    impact: string;
    lineNumber: number;
}

export interface AutoFixCodeStyleDetail {
    fileName: string;
    results: AutoFixCodeStyleDetailResult[];
}

// 编码规范检查结果信息
export interface CodeStyleResultInfo {
    codestyleFix: boolean;
}

export interface IntimateUser {
    /**
     * 亲密度分值（最大为1 越高表明越亲密）
     */
    score: number;
    /**
     * 每个人的邮箱都对应一个唯一的uid
     */
    uid: number | string;
    /**
     * 用户名
     */
    username: string;
}

export interface CommentTag {
    /**
     * 内置标签的id
     */
    id: number;
    /**
     * 内置标签的类型
     */
    title: string;
    /**
     * 内置标签的描述
     */
    comment: string;
}

export interface CodeReviewOverview {
    /**
     * 代码库名称
     */
    repoName: string;
    /**
     * 评审changeNumber
     */
    changeNumber: string;
    /**
     * 评审maxPatchId
     */
    maxPatchId: string;
    /**
     * 评审打分时间戳
     */
    codeReviewTimestamp: string;
     /**
     * 评审时长
     */
    codeReviewSeconds: string;
    /**
     * 评审页链接
     */
    codeReviewUrl: string;
}

export interface CommentRateInfo {
    checkJobId: number;
    incCommentedFuncNum: number;
    incTotalFuncNum: number;
    incUncommentedFuncNum: number;
    incUncommentedAPINum: number;
    rate: string;
}

export type AutoCompletionCodeJobClassify = 'CODE_STYLE' | 'COMMENT_RATE_CHECK' | 'UT_GENERATE';

export type EagleJobIconStatus = 'FAILED' | 'STAGE' | 'NONE' | 'SUCCESS' | 'INITIALIZING' | 'CHECKING' | 'WARNING';

export interface AdoptReviewSuggestion {
    filePath: string;
    commentType: string;
    startLine: number;
    startCharacter: number;
    endLine: number;
    endCharacter: number;
    suggestCode: string | null;
    uuid: string;
    index: number;
}

export interface APIFixData {
    taskid: number;
    api_nums: number;
    create_time: string;
    update_time: string;
    creator: string;
    language: string;
    status: string;
}

export interface APIFixResponse {
    code: number;
    msg: string;
    data: APIFixData[];
}
